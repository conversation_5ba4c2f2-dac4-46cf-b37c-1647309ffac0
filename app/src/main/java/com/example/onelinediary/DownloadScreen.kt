package com.example.onelinediary

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Environment
import android.util.Log
import android.widget.Toast
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.border
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.documentfile.provider.DocumentFile
import com.example.onelinediary.components.standardOutlinedButtonColors
import com.example.onelinediary.components.standardButtonColors
import com.example.onelinediary.components.standardButtonModifier
import com.example.onelinediary.components.standardButtonPadding
import com.example.onelinediary.components.standardButtonShape
import com.example.onelinediary.components.backButtonModifier
import com.example.onelinediary.stringResource
import com.example.onelinediary.ui.theme.ButtonGreen
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.InputStream
import java.text.SimpleDateFormat
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

@Composable
fun DownloadScreen(
    onBack: () -> Unit,
    saveDateManager: SaveDateManager
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    // State for selected content types
    val selectedContentTypes = remember { mutableStateOf(ContentType.values().toSet()) }

    // State for selected time period
    var selectedTimePeriod by remember { mutableStateOf(DownloadTimePeriod.ALL) }

    // State for download progress
    var isDownloading by remember { mutableStateOf(false) }
    var downloadComplete by remember { mutableStateOf(false) }
    var downloadMessage by remember { mutableStateOf("") }

    // State for folder selection
    var selectedFolderUri by remember { mutableStateOf<Uri?>(null) }
    var selectedFolderPath by remember { mutableStateOf("Documents (Default)") }

    // SharedPreferences for storing folder preference
    val prefs = remember { context.getSharedPreferences("download_prefs", Context.MODE_PRIVATE) }

    // Load saved folder preference
    LaunchedEffect(Unit) {
        val savedFolderUri = prefs.getString("selected_folder_uri", null)
        if (savedFolderUri != null) {
            try {
                val uri = Uri.parse(savedFolderUri)
                selectedFolderUri = uri
                // Get display name for the folder
                val documentFile = DocumentFile.fromTreeUri(context, uri)
                selectedFolderPath = documentFile?.name ?: "Custom Folder"
            } catch (e: Exception) {
                Log.e("DownloadScreen", "Error loading saved folder URI", e)
            }
        }
    }

    // Folder picker launcher
    val folderPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.OpenDocumentTree()
    ) { uri: Uri? ->
        uri?.let {
            selectedFolderUri = it
            // Persist the URI for future use
            context.contentResolver.takePersistableUriPermission(
                it,
                Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
            )
            // Save to preferences
            prefs.edit().putString("selected_folder_uri", it.toString()).apply()

            // Get display name for the folder
            val documentFile = DocumentFile.fromTreeUri(context, it)
            selectedFolderPath = documentFile?.name ?: "Custom Folder"

            Log.d("DownloadScreen", "Selected folder: $selectedFolderPath at URI: $it")
        }
    }


    // Function to start the download process to selected folder
    fun startDownload() {
        isDownloading = true
        downloadComplete = false
        downloadMessage = "Preparing files..."

        coroutineScope.launch {
            try {
                val result = downloadToSelectedFolder(
                    context = context,
                    selectedContentTypes = selectedContentTypes.value,
                    saveDateManager = saveDateManager,
                    timePeriod = selectedTimePeriod,
                    customFolderUri = selectedFolderUri
                )

                downloadMessage = result
                downloadComplete = true
                isDownloading = false
            } catch (e: Exception) {
                Log.e("DownloadScreen", "Error during download", e)
                downloadMessage = "Error: ${e.message}"
                downloadComplete = true
                isDownloading = false
            }
        }
    }

    // Get the background color from the theme
    val backgroundColor = MaterialTheme.colorScheme.background

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .padding(16.dp)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Top
    ) {
        // Top navigation row with back button
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Back button
            Button(
                onClick = onBack,
                modifier = backButtonModifier(),
                contentPadding = standardButtonPadding,
                colors = standardButtonColors(),
                shape = standardButtonShape
            ) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                    contentDescription = "Back",
                    tint = MaterialTheme.colorScheme.onBackground
                )
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Title
        Text(
            text = stringResource(R.string.download_files),
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.fillMaxWidth(),
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Content type selection
        Text(
            text = "Select content types to download:",
            style = MaterialTheme.typography.titleMedium
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Checkboxes for content types
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            LazyRow(
                modifier = Modifier.padding(16.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(ContentType.values()) { contentType ->
                    Column(
                        modifier = Modifier
                            .clickable {
                                selectedContentTypes.value = if (selectedContentTypes.value.contains(contentType)) {
                                    selectedContentTypes.value - contentType
                                } else {
                                    selectedContentTypes.value + contentType
                                }
                            },
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Checkbox(
                            checked = selectedContentTypes.value.contains(contentType),
                            onCheckedChange = { isChecked ->
                                selectedContentTypes.value = if (isChecked) {
                                    selectedContentTypes.value + contentType
                                } else {
                                    selectedContentTypes.value - contentType
                                }
                            }
                        )

                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = contentType.name.capitalize(),
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Time period selection
        Text(
            text = "Select time period to download:",
            style = MaterialTheme.typography.titleMedium
        )

        Spacer(modifier = Modifier.height(8.dp))

        // Radio buttons for time periods
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            LazyRow(
                modifier = Modifier.padding(16.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(DownloadTimePeriod.values()) { timePeriod ->
                    Column(
                        modifier = Modifier
                            .clickable { selectedTimePeriod = timePeriod },
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        RadioButton(
                            selected = selectedTimePeriod == timePeriod,
                            onClick = { selectedTimePeriod = timePeriod }
                        )

                        Spacer(modifier = Modifier.height(4.dp))

                        Text(
                            text = when(timePeriod) {
                                DownloadTimePeriod.DAY -> "Today only"
                                DownloadTimePeriod.WEEK -> "Last 7 days"
                                DownloadTimePeriod.MONTH -> "Last 30 days"
                                DownloadTimePeriod.YEAR -> "Last 365 days"
                                DownloadTimePeriod.ALL -> "All dates"
                            },
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Folder selection
        Text(
            text = stringResource(R.string.select_folder),
            style = MaterialTheme.typography.titleMedium
        )

        Spacer(modifier = Modifier.height(8.dp))

        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Current folder:",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                        Text(
                            text = selectedFolderPath,
                            style = MaterialTheme.typography.bodyLarge,
                            color = MaterialTheme.colorScheme.onSurface
                        )
                    }

                    OutlinedButton(
                        onClick = {
                            folderPickerLauncher.launch(null)
                        },
                        contentPadding = standardButtonPadding,
                        colors = standardOutlinedButtonColors(),
                        shape = standardButtonShape
                    ) {
                        Text(
                            "📁 Choose Folder",
                            color = MaterialTheme.colorScheme.onBackground
                        )
                    }
                }

                if (selectedFolderUri != null) {
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedButton(
                        onClick = {
                            selectedFolderUri = null
                            selectedFolderPath = "Documents (Default)"
                            prefs.edit().remove("selected_folder_uri").apply()
                        },
                        modifier = Modifier.fillMaxWidth(),
                        contentPadding = standardButtonPadding,
                        colors = standardOutlinedButtonColors(),
                        shape = standardButtonShape
                    ) {
                        Text(
                            "Use Default Folder (Documents)",
                            color = MaterialTheme.colorScheme.onBackground
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Download button
        Button(
            onClick = {
                if (selectedContentTypes.value.isEmpty()) {
                    Toast.makeText(context, "Please select at least one content type", Toast.LENGTH_SHORT).show()
                } else {
                    startDownload()
                }
            },
            modifier = standardButtonModifier(
                Modifier.fillMaxWidth(),
                isDisabled = isDownloading || selectedContentTypes.value.isEmpty()
            ),
            contentPadding = standardButtonPadding,
            colors = standardButtonColors(),
            shape = standardButtonShape,
            enabled = !isDownloading && selectedContentTypes.value.isNotEmpty()
        ) {
            Icon(
                painter = androidx.compose.ui.res.painterResource(id = R.drawable.ic_download),
                contentDescription = "Download",
                tint = MaterialTheme.colorScheme.onBackground
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                "Download Selected Content",
                color = MaterialTheme.colorScheme.onBackground
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Progress indicator and status message
        if (isDownloading) {
            LinearProgressIndicator(
                modifier = Modifier.fillMaxWidth()
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = downloadMessage,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.fillMaxWidth(),
                textAlign = TextAlign.Center
            )
        }

        // Success message
        if (downloadComplete) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = if (downloadMessage.startsWith("Error"))
                        MaterialTheme.colorScheme.errorContainer
                    else
                        MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    if (!downloadMessage.startsWith("Error")) {
                        // Use text instead of icon to avoid potential issues
                        Text(
                            text = "✓",
                            style = MaterialTheme.typography.headlineSmall,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = downloadMessage,
                        style = MaterialTheme.typography.bodyLarge,
                        color = if (downloadMessage.startsWith("Error"))
                            MaterialTheme.colorScheme.onErrorContainer
                        else
                            MaterialTheme.colorScheme.onPrimaryContainer
                    )
                }
            }
        }
    }
}

/**
 * Downloads content of the selected types for the selected time period to selected folder
 */
private suspend fun downloadToSelectedFolder(
    context: Context,
    selectedContentTypes: Set<ContentType>,
    saveDateManager: SaveDateManager,
    timePeriod: DownloadTimePeriod,
    customFolderUri: Uri?
): String = withContext(Dispatchers.IO) {
    try {
        // Get dates with content for the selected time period
        val filteredDates = saveDateManager.getDatesInTimePeriod(timePeriod)
        if (filteredDates.isEmpty()) {
            return@withContext "No content found to download for the selected time period"
        }

        // Log the time period and number of dates
        Log.d("DownloadScreen", "Downloading content for time period: $timePeriod, found ${filteredDates.size} dates")

        // Create a subfolder for the download with today's date and selected time period
        val today = SimpleDateFormat("yyyyMMdd", Locale.getDefault()).format(Date())

        // Get the time period name for the folder
        val timePeriodName = when(timePeriod) {
            DownloadTimePeriod.DAY -> "day"
            DownloadTimePeriod.WEEK -> "week"
            DownloadTimePeriod.MONTH -> "month"
            DownloadTimePeriod.YEAR -> "year"
            DownloadTimePeriod.ALL -> "all"
        }

        val downloadFolderName = "OnLiDi_download_${today}_$timePeriodName"

        // Handle folder creation based on whether custom folder is selected
        val downloadFolder: Any
        val isCustomFolder = customFolderUri != null

        if (isCustomFolder) {
            // Use Storage Access Framework for custom folder
            val parentDocumentFile = DocumentFile.fromTreeUri(context, customFolderUri!!)
            if (parentDocumentFile == null || !parentDocumentFile.exists()) {
                return@withContext "Error: Selected folder is not accessible"
            }

            // Create or get the download subfolder
            val existingFolder = parentDocumentFile.findFile(downloadFolderName)
            downloadFolder = if (existingFolder != null && existingFolder.isDirectory) {
                existingFolder
            } else {
                parentDocumentFile.createDirectory(downloadFolderName)
                    ?: return@withContext "Error: Could not create download folder in selected location"
            }

            Log.d("DownloadScreen", "Download folder created using SAF: ${downloadFolder.name}")
        } else {
            // Use traditional file system for Documents folder
            val documentsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOCUMENTS)
            if (!documentsDir.exists()) {
                documentsDir.mkdirs()
            }

            downloadFolder = File(documentsDir, downloadFolderName)
            if (!downloadFolder.exists()) {
                val created = downloadFolder.mkdirs()
                if (!created) {
                    return@withContext "Error: Could not create download folder"
                }
            }

            Log.d("DownloadScreen", "Download folder created: ${downloadFolder.absolutePath}")
        }

        var filesCopied = 0

        // Define storage locations
        val textFolder = File(Environment.getExternalStoragePublicDirectory(
            Environment.DIRECTORY_DOCUMENTS), "OnLiDi")
        val photoFolder = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES)
        val videoFolder = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES)
        val audioFolder = context.getExternalFilesDir(Environment.DIRECTORY_MUSIC)

        Log.d("DownloadScreen", "Storage locations: " +
            "Text: ${textFolder.absolutePath}, " +
            "Photos: ${photoFolder?.absolutePath}, " +
            "Videos: ${videoFolder?.absolutePath}, " +
            "Audio: ${audioFolder?.absolutePath}")

        // Process each date in the filtered set
        for (dateString in filteredDates) {
            // Create a calendar from the date string
            val date = Calendar.getInstance()
            date.time = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).parse(dateString) ?: continue

            // Get content for this date
            val contentMap = saveDateManager.getContentForDate(date)

            // Filter by selected content types
            val filteredContent = contentMap.filter { (contentType, _) ->
                selectedContentTypes.contains(contentType)
            }

            if (filteredContent.isNotEmpty()) {
                // Create a folder for this date
                val dateFolder = createDateFolder(downloadFolder, dateString)
                if (dateFolder == null) {
                    Log.e("DownloadScreen", "Failed to create date folder: $dateString")
                    continue
                }

                Log.d("DownloadScreen", "Processing date: $dateString with content types: ${filteredContent.keys}")

                // Copy each content file
                for ((contentType, content) in filteredContent) {
                    when (contentType) {
                        ContentType.TEXT -> {
                            // For text content, copy the text file directly
                            val sourceFileName = "${dateString}_${contentType.name.lowercase()}.txt"
                            val sourceFile = File(textFolder, sourceFileName)

                            if (sourceFile.exists()) {
                                if (copyFileToDestination(context, sourceFile, dateFolder, sourceFileName)) {
                                    filesCopied++
                                    Log.d("DownloadScreen", "Copied text file: $sourceFileName")
                                } else {
                                    Log.e("DownloadScreen", "Failed to copy text file: $sourceFileName")
                                }
                            } else {
                                Log.e("DownloadScreen", "Text file not found: $sourceFileName")
                            }
                        }

                        ContentType.PHOTO -> {
                            // Skip metadata file and just copy the actual photo file
                            val photoFiles = photoFolder?.listFiles()
                            if (photoFiles != null) {
                                // Try to find a photo file created on this date
                                val dateFormat = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
                                val datePart = dateFormat.format(date.time)

                                val photoFile = photoFiles.find {
                                    it.name.startsWith("PHOTO_${datePart}") ||
                                    it.lastModified().let { time ->
                                        val fileDate = Calendar.getInstance()
                                        fileDate.timeInMillis = time
                                        dateFormat.format(fileDate.time) == datePart
                                    }
                                }

                                if (photoFile != null) {
                                    val photoFileName = "${dateString}_photo.jpg"
                                    if (copyFileToDestination(context, photoFile, dateFolder, photoFileName)) {
                                        filesCopied++
                                        Log.d("DownloadScreen", "Copied photo file: ${photoFile.name} as $photoFileName")
                                    } else {
                                        Log.e("DownloadScreen", "Failed to copy photo file: ${photoFile.name}")
                                    }
                                } else {
                                    Log.e("DownloadScreen", "No matching photo file found for date: $dateString")
                                }
                            }
                        }

                        ContentType.VIDEO -> {
                            // Skip metadata file and just copy the actual video file
                            val videoFiles = videoFolder?.listFiles()
                            if (videoFiles != null) {
                                // Try to find a video file created on this date
                                val dateFormat = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
                                val datePart = dateFormat.format(date.time)

                                val videoFile = videoFiles.find {
                                    it.name.startsWith("VIDEO_${datePart}") ||
                                    it.lastModified().let { time ->
                                        val fileDate = Calendar.getInstance()
                                        fileDate.timeInMillis = time
                                        dateFormat.format(fileDate.time) == datePart
                                    }
                                }

                                if (videoFile != null) {
                                    val videoFileName = "${dateString}_video.mp4"
                                    if (copyFileToDestination(context, videoFile, dateFolder, videoFileName)) {
                                        filesCopied++
                                        Log.d("DownloadScreen", "Copied video file: ${videoFile.name} as $videoFileName")
                                    } else {
                                        Log.e("DownloadScreen", "Failed to copy video file: ${videoFile.name}")
                                    }
                                } else {
                                    Log.e("DownloadScreen", "No matching video file found for date: $dateString")
                                }
                            }
                        }

                        ContentType.AUDIO -> {
                            // Skip metadata file and just copy the actual audio file
                            val audioFiles = audioFolder?.listFiles()
                            if (audioFiles != null) {
                                // Try to find an audio file created on this date
                                val dateFormat = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
                                val datePart = dateFormat.format(date.time)

                                val audioFile = audioFiles.find {
                                    it.name.startsWith("AUDIO_${datePart}") ||
                                    it.lastModified().let { time ->
                                        val fileDate = Calendar.getInstance()
                                        fileDate.timeInMillis = time
                                        dateFormat.format(fileDate.time) == datePart
                                    }
                                }

                                if (audioFile != null) {
                                    val audioFileName = "${dateString}_audio.3gp"
                                    if (copyFileToDestination(context, audioFile, dateFolder, audioFileName)) {
                                        filesCopied++
                                        Log.d("DownloadScreen", "Copied audio file: ${audioFile.name} as $audioFileName")
                                    } else {
                                        Log.e("DownloadScreen", "Failed to copy audio file: ${audioFile.name}")
                                    }
                                } else {
                                    Log.e("DownloadScreen", "No matching audio file found for date: $dateString")
                                }
                            }
                        }

                        ContentType.MOOD -> {
                            // For mood content, copy the text file directly
                            val sourceFileName = "${dateString}_${contentType.name.lowercase()}.txt"
                            val sourceFile = File(textFolder, sourceFileName)

                            if (sourceFile.exists()) {
                                if (copyFileToDestination(context, sourceFile, dateFolder, sourceFileName)) {
                                    filesCopied++
                                    Log.d("DownloadScreen", "Copied mood file: $sourceFileName")
                                } else {
                                    Log.e("DownloadScreen", "Failed to copy mood file: $sourceFileName")
                                }
                            } else {
                                Log.e("DownloadScreen", "Mood file not found: $sourceFileName")
                            }
                        }
                    }
                }
            }
        }

        // Create a user-friendly time period description
        val timePeriodText = when(timePeriod) {
            DownloadTimePeriod.DAY -> "today"
            DownloadTimePeriod.WEEK -> "the last 7 days"
            DownloadTimePeriod.MONTH -> "the last 30 days"
            DownloadTimePeriod.YEAR -> "the last 365 days"
            DownloadTimePeriod.ALL -> "all time"
        }

        val folderPath = getFolderDisplayPath(downloadFolder)
        val folderName = when (downloadFolder) {
            is File -> downloadFolder.name
            is DocumentFile -> downloadFolder.name ?: "OnLiDi_download"
            else -> "OnLiDi_download"
        }

        return@withContext if (filesCopied > 0) {
            if (isCustomFolder) {
                "Successfully downloaded $filesCopied files from $timePeriodText to $folderPath/$folderName"
            } else {
                "Successfully downloaded $filesCopied files from $timePeriodText to Documents/$folderName"
            }
        } else {
            "No files found matching the selected content types for $timePeriodText"
        }
    } catch (e: Exception) {
        Log.e("DownloadScreen", "Error downloading content", e)
        return@withContext "Error: ${e.message}"
    }
}

/**
 * Helper function to get a File from a Uri
 */
private fun getFileFromUri(context: Context, uri: Uri): File? {
    try {
        Log.d("DownloadScreen", "Trying to resolve file from URI: $uri")

        // Try to get the file path directly
        val path = uri.path
        if (path != null) {
            val file = File(path)
            if (file.exists()) {
                Log.d("DownloadScreen", "Found file directly from path: ${file.absolutePath}")
                return file
            }
        }

        // For content URIs, try to determine the actual file
        if (uri.scheme == "content") {
            // Try to get the actual file path from the content URI
            try {
                context.contentResolver.openInputStream(uri)?.use { inputStream ->
                    Log.d("DownloadScreen", "Successfully opened input stream for URI: $uri")
                    return createTempFileFromInputStream(context, inputStream, uri)
                }
            } catch (e: Exception) {
                Log.e("DownloadScreen", "Error opening input stream for URI: $uri", e)
            }

            // Check if it's a file from our app's storage
            val appStoragePaths = listOf(
                context.getExternalFilesDir(Environment.DIRECTORY_PICTURES),
                context.getExternalFilesDir(Environment.DIRECTORY_MOVIES),
                context.getExternalFilesDir(Environment.DIRECTORY_MUSIC)
            )

            for (storagePath in appStoragePaths) {
                if (storagePath != null) {
                    // Look for files in this directory
                    val files = storagePath.listFiles()
                    if (files != null) {
                        for (file in files) {
                            val fileUri = Uri.fromFile(file)
                            if (fileUri.lastPathSegment == uri.lastPathSegment) {
                                Log.d("DownloadScreen", "Found matching file in app storage: ${file.absolutePath}")
                                return file
                            }
                        }
                    }
                }
            }
        }

        // Try to extract file path from URI string for non-content URIs
        val uriString = uri.toString()
        if (uriString.startsWith("file://")) {
            val filePath = uriString.substring("file://".length)
            val file = File(filePath)
            if (file.exists()) {
                Log.d("DownloadScreen", "Found file from file:// URI: ${file.absolutePath}")
                return file
            }
        }

        // For URIs that might be in the format of a string path
        if (!uriString.contains("://")) {
            val file = File(uriString)
            if (file.exists()) {
                Log.d("DownloadScreen", "Found file from direct string path: ${file.absolutePath}")
                return file
            }
        }
    } catch (e: Exception) {
        Log.e("DownloadScreen", "Error getting file from URI: $uri", e)
    }

    Log.e("DownloadScreen", "Could not resolve file from URI: $uri")
    return null
}

/**
 * Creates a temporary file from an input stream
 */
private fun createTempFileFromInputStream(context: Context, inputStream: InputStream, uri: Uri): File? {
    return try {
        val extension = when {
            uri.toString().endsWith(".jpg", ignoreCase = true) -> ".jpg"
            uri.toString().endsWith(".jpeg", ignoreCase = true) -> ".jpeg"
            uri.toString().endsWith(".png", ignoreCase = true) -> ".png"
            uri.toString().endsWith(".mp4", ignoreCase = true) -> ".mp4"
            uri.toString().endsWith(".3gp", ignoreCase = true) -> ".3gp"
            uri.toString().contains("image", ignoreCase = true) -> ".jpg"
            uri.toString().contains("video", ignoreCase = true) -> ".mp4"
            uri.toString().contains("audio", ignoreCase = true) -> ".3gp"
            else -> ".tmp"
        }

        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "temp_${timestamp}_${uri.lastPathSegment?.replace("/", "_") ?: "file"}$extension"

        val tempDir = context.cacheDir
        val tempFile = File(tempDir, fileName)

        FileOutputStream(tempFile).use { outputStream ->
            inputStream.copyTo(outputStream)
        }

        Log.d("DownloadScreen", "Created temporary file from input stream: ${tempFile.absolutePath}")
        tempFile
    } catch (e: Exception) {
        Log.e("DownloadScreen", "Error creating temporary file from input stream", e)
        null
    }
}

/**
 * Extract media URI from content text
 */
private fun extractMediaUriFromContent(content: String): String? {
    Log.d("DownloadScreen", "Extracting URI from content: $content")

    // Try different patterns to extract URI
    return when {
        // Photo formats
        content.contains("Photo selected from gallery:") -> {
            val uri = content.substringAfter("Photo selected from gallery:").trim()
            Log.d("DownloadScreen", "Extracted photo gallery URI: $uri")
            uri
        }
        content.contains("Photo captured with camera:") -> {
            val uri = content.substringAfter("Photo captured with camera:").trim()
            Log.d("DownloadScreen", "Extracted photo camera URI: $uri")
            uri
        }
        content.contains("gallery:") -> {
            val uri = content.substringAfter("gallery:").trim()
            Log.d("DownloadScreen", "Extracted gallery URI: $uri")
            uri
        }

        // Video formats
        content.contains("Video selected from device:") -> {
            val uri = content.substringAfter("Video selected from device:").trim()
            Log.d("DownloadScreen", "Extracted video device URI: $uri")
            uri
        }
        content.contains("Video recorded:") -> {
            val uri = content.substringAfter("Video recorded:").trim()
            Log.d("DownloadScreen", "Extracted recorded video URI: $uri")
            uri
        }

        // Audio formats
        content.contains("Audio file selected:") -> {
            val uri = content.substringAfter("Audio file selected:").trim()
            Log.d("DownloadScreen", "Extracted audio file URI: $uri")
            uri
        }
        content.contains("Audio selected from device:") -> {
            val uri = content.substringAfter("Audio selected from device:").trim()
            Log.d("DownloadScreen", "Extracted audio device URI: $uri")
            uri
        }
        content.contains("Audio recorded:") -> {
            val uri = content.substringAfter("Audio recorded:").trim()
            Log.d("DownloadScreen", "Extracted recorded audio URI: $uri")
            uri
        }

        // Mood formats
        content.contains("Mood:") -> {
            val mood = content.substringAfter("Mood:").trim()
            Log.d("DownloadScreen", "Extracted mood: $mood")
            mood
        }

        // Generic URI pattern
        content.contains("URI:") -> {
            val uriPattern = "URI: (.*?)$".toRegex()
            val uriMatch = uriPattern.find(content)
            val uri = uriMatch?.groupValues?.get(1)
            Log.d("DownloadScreen", "Extracted URI using pattern: $uri")
            uri
        }

        // Try to find any content:// or file:// URI in the text
        content.contains("content://") -> {
            val startIndex = content.indexOf("content://")
            val endIndex = content.indexOf(" ", startIndex).takeIf { it > 0 } ?: content.length
            val uri = content.substring(startIndex, endIndex)
            Log.d("DownloadScreen", "Extracted content:// URI: $uri")
            uri
        }
        content.contains("file://") -> {
            val startIndex = content.indexOf("file://")
            val endIndex = content.indexOf(" ", startIndex).takeIf { it > 0 } ?: content.length
            val uri = content.substring(startIndex, endIndex)
            Log.d("DownloadScreen", "Extracted file:// URI: $uri")
            uri
        }

        // No URI found
        else -> {
            Log.e("DownloadScreen", "No URI pattern found in content")
            null
        }
    }
}

/**
 * Extension function to capitalize the first letter of a string
 */
private fun String.capitalize(): String {
    return if (this.isNotEmpty()) {
        this[0].uppercase() + this.substring(1).lowercase()
    } else {
        this
    }
}

/**
 * Helper function to create a date folder in either File or DocumentFile
 */
private fun createDateFolder(parentFolder: Any, dateString: String): Any? {
    return when (parentFolder) {
        is File -> {
            val dateFolder = File(parentFolder, dateString)
            if (!dateFolder.exists()) {
                val created = dateFolder.mkdirs()
                if (created) dateFolder else null
            } else {
                dateFolder
            }
        }
        is DocumentFile -> {
            val existingFolder = parentFolder.findFile(dateString)
            if (existingFolder != null && existingFolder.isDirectory) {
                existingFolder
            } else {
                parentFolder.createDirectory(dateString)
            }
        }
        else -> null
    }
}

/**
 * Helper function to copy a file to either File or DocumentFile destination
 */
private fun copyFileToDestination(
    context: Context,
    sourceFile: File,
    destinationFolder: Any,
    fileName: String
): Boolean {
    return try {
        when (destinationFolder) {
            is File -> {
                val destFile = File(destinationFolder, fileName)
                FileInputStream(sourceFile).use { inputStream ->
                    FileOutputStream(destFile).use { outputStream ->
                        inputStream.copyTo(outputStream)
                    }
                }
                true
            }
            is DocumentFile -> {
                val destFile = destinationFolder.createFile("*/*", fileName)
                if (destFile != null) {
                    FileInputStream(sourceFile).use { inputStream ->
                        context.contentResolver.openOutputStream(destFile.uri)?.use { outputStream ->
                            inputStream.copyTo(outputStream)
                        }
                    }
                    true
                } else {
                    false
                }
            }
            else -> false
        }
    } catch (e: Exception) {
        Log.e("DownloadScreen", "Error copying file $fileName: ${e.message}")
        false
    }
}

/**
 * Helper function to get folder path for display purposes
 */
private fun getFolderDisplayPath(folder: Any): String {
    return when (folder) {
        is File -> folder.absolutePath
        is DocumentFile -> folder.name ?: "Custom Folder"
        else -> "Unknown"
    }
}


