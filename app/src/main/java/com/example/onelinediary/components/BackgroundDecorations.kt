package com.example.onelinediary.components

import android.util.Log
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.example.onelinediary.ui.theme.DecorationTheme
import com.example.onelinediary.ui.theme.LocalAppTheme
import com.example.onelinediary.components.getSoftPastelColor
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt
import kotlin.random.Random

// Data class to track placed decorations for collision detection
data class PlacedDecoration(
    val center: Offset,
    val size: Float
)

// Function to check if two decorations would overlap
fun decorationsOverlap(decoration1: PlacedDecoration, decoration2: PlacedDecoration, minSpacing: Float = 0f): Boolean {
    val distance = sqrt(
        (decoration1.center.x - decoration2.center.x) * (decoration1.center.x - decoration2.center.x) +
        (decoration1.center.y - decoration2.center.y) * (decoration1.center.y - decoration2.center.y)
    )
    val combinedRadius = (decoration1.size + decoration2.size) / 2f + minSpacing
    return distance < combinedRadius
}

@Composable
fun BackgroundDecorations(
    decorationTheme: DecorationTheme,
    modifier: Modifier = Modifier
) {
    Log.d("BackgroundDecorations", "Drawing decorations with theme: $decorationTheme")
    when (decorationTheme) {
        DecorationTheme.NONE -> {
            Log.d("BackgroundDecorations", "No decorations for theme: $decorationTheme")
            // No decorations
        }
        DecorationTheme.STANDARD -> {
            Log.d("BackgroundDecorations", "Drawing standard decorations")
            StandardDecorations(modifier = modifier)
        }
        DecorationTheme.ROMANTIC -> {
            Log.d("BackgroundDecorations", "Drawing romantic decorations")
            RomanticDecorations(modifier = modifier)
        }
    }
}

@Composable
fun TestDecorations(modifier: Modifier = Modifier) {
    Log.d("TestDecorations", "TestDecorations composable called")

    Canvas(modifier = modifier.fillMaxSize()) {
        Log.d("TestDecorations", "Canvas drawing - size: ${size.width} x ${size.height}")

        // Draw a big red circle in the center - impossible to miss!
        drawCircle(
            color = Color.Red,
            radius = 100f,
            center = Offset(size.width / 2, size.height / 2)
        )

        // Draw a blue rectangle in top-left
        drawRect(
            color = Color.Blue,
            topLeft = Offset(50f, 50f),
            size = Size(200f, 100f)
        )

        Log.d("TestDecorations", "Canvas drawing completed")
    }
}

@Composable
fun RomanticDecorations(modifier: Modifier = Modifier) {
    val density = LocalDensity.current
    val primaryColor = MaterialTheme.colorScheme.primary
    val onBackgroundColor = MaterialTheme.colorScheme.onBackground
    val softPastelColor = getSoftPastelColor() // Get the disabled button color

    Canvas(modifier = modifier.fillMaxSize()) {
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Create a seeded random for consistent decoration placement
        val random = Random(42) // Fixed seed for consistent placement

        // Use the exact same color tint as the chosen theme color for both flowers and hearts
        val decorationColor = primaryColor

        // List to track all placed decorations to avoid overlaps
        val placedDecorations = mutableListOf<PlacedDecoration>()
        val minSpacing = with(density) { 32.dp.toPx() } // Minimum spacing between decorations

        // Draw hand-drawn style hearts scattered across the background with collision detection
        var heartsPlaced = 0
        var attempts = 0
        val maxHearts = 30 // Reduced for better spacing with larger hearts
        val maxAttempts = 500

        while (heartsPlaced < maxHearts && attempts < maxAttempts) {
            attempts++

            // Random position across the entire canvas
            val x = random.nextFloat() * canvasWidth
            val y = random.nextFloat() * canvasHeight

            // Size variation for hand-drawn hearts (larger sizes for better visibility)
            val sizeCategory = random.nextFloat()
            val size = with(density) {
                when {
                    sizeCategory < 0.3f -> (20 + random.nextFloat() * 15).dp.toPx() // Small: 20-35dp
                    sizeCategory < 0.6f -> (40 + random.nextFloat() * 20).dp.toPx() // Medium: 40-60dp
                    else -> (65 + random.nextFloat() * 25).dp.toPx()               // Large: 65-90dp
                }
            }

            val newDecoration = PlacedDecoration(Offset(x, y), size)

            // Check if this position would overlap with any existing decorations
            val wouldOverlap = placedDecorations.any { existing ->
                decorationsOverlap(existing, newDecoration, minSpacing)
            }

            // Also check if decoration is too close to canvas edges
            val margin = size / 2f + minSpacing
            val tooCloseToEdge = x < margin || y < margin ||
                               x > canvasWidth - margin || y > canvasHeight - margin

            if (!wouldOverlap && !tooCloseToEdge) {
                // Safe to place this heart
                placedDecorations.add(newDecoration)
                heartsPlaced++

                // Use soft pastel color (disabled button color) with some transparency for artistic effect
                val alpha = 0.6f + random.nextFloat() * 0.3f // 60-90% opacity for bold effect

                drawHandDrawnHeart(
                    center = Offset(x, y),
                    size = size,
                    color = softPastelColor.copy(alpha = alpha),
                    random = random
                )
            }
        }
    }
}

@Composable
fun StandardDecorations(modifier: Modifier = Modifier) {
    val density = LocalDensity.current

    Canvas(modifier = modifier.fillMaxSize()) {
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Create a seeded random for consistent decoration placement
        val random = Random(123) // Different seed from romantic theme

        // List to track all placed decorations to avoid overlaps
        val placedDecorations = mutableListOf<PlacedDecoration>()
        val minSpacing = with(density) { 40.dp.toPx() } // Larger spacing for minimalistic look

        // Draw manga-style speed line sketches scattered across the background
        var sketchesPlaced = 0
        var attempts = 0
        val maxSketches = 12 // Fewer sketches to avoid overwhelming the background
        val maxAttempts = 300

        while (sketchesPlaced < maxSketches && attempts < maxAttempts) {
            attempts++

            // Random position across the entire canvas
            val x = random.nextFloat() * canvasWidth
            val y = random.nextFloat() * canvasHeight

            // Size variation for speed line sketches (medium to large areas)
            val sizeCategory = random.nextFloat()
            val size = with(density) {
                when {
                    sizeCategory < 0.3f -> (40 + random.nextFloat() * 30).dp.toPx() // Medium: 40-70dp
                    sizeCategory < 0.7f -> (75 + random.nextFloat() * 35).dp.toPx() // Large: 75-110dp
                    else -> (115 + random.nextFloat() * 45).dp.toPx()              // Extra Large: 115-160dp
                }
            }

            val newDecoration = PlacedDecoration(Offset(x, y), size)

            // Check if this position would overlap with any existing decorations
            val wouldOverlap = placedDecorations.any { existing ->
                decorationsOverlap(existing, newDecoration, minSpacing)
            }

            // Also check if decoration is too close to canvas edges
            val margin = size / 2f + minSpacing
            val tooCloseToEdge = x < margin || y < margin ||
                               x > canvasWidth - margin || y > canvasHeight - margin

            if (!wouldOverlap && !tooCloseToEdge) {
                // Safe to place this sketch
                placedDecorations.add(newDecoration)
                sketchesPlaced++

                // Use pure black with low transparency for subtle manga-style effect
                val alpha = 0.08f + random.nextFloat() * 0.12f // 8-20% opacity for subtle effect

                drawMangaSpeedLines(
                    center = Offset(x, y),
                    size = size,
                    color = Color.Black.copy(alpha = alpha),
                    random = random
                )
            }
        }
    }
}

fun DrawScope.drawHandDrawnHeart(
    center: Offset,
    size: Float,
    color: Color,
    random: Random
) {
    // Create multiple paths for brushstroke effect
    val numStrokes = 3 + random.nextInt(3) // 3-5 strokes for texture

    for (strokeIndex in 0 until numStrokes) {
        val path = Path()

        // Add randomness for hand-drawn effect
        val jitterAmount = size * 0.02f
        val jitter = { random.nextFloat() * jitterAmount - jitterAmount / 2f }

        // Heart dimensions with slight variations
        val heartWidth = size * (1.0f + random.nextFloat() * 0.2f - 0.1f) // ±10% variation
        val heartHeight = size * (0.9f + random.nextFloat() * 0.2f - 0.1f) // ±10% variation

        // Bottom point with jitter
        val startX = center.x + jitter()
        val startY = center.y + heartHeight * 0.4f + jitter()

        path.moveTo(startX, startY)

        // Left side - straight line to left curve start
        val leftCurveStartX = startX - heartWidth * 0.3f + jitter()
        val leftCurveStartY = startY - heartHeight * 0.6f + jitter()
        path.lineTo(leftCurveStartX, leftCurveStartY)

        // Left top bump (first curve) - asymmetrical
        val leftCurveTopX = startX - heartWidth * 0.25f + jitter()
        val leftCurveTopY = startY - heartHeight * 0.9f + jitter()
        val leftCurveEndX = startX + jitter()
        val leftCurveEndY = startY - heartHeight * 0.7f + jitter()

        path.quadraticTo(leftCurveTopX, leftCurveTopY, leftCurveEndX, leftCurveEndY)

        // Right top bump (second curve) - slightly different for asymmetry
        val rightCurveTopX = startX + heartWidth * 0.28f + jitter()
        val rightCurveTopY = startY - heartHeight * 0.85f + jitter()
        val rightCurveEndX = startX + heartWidth * 0.3f + jitter()
        val rightCurveEndY = startY - heartHeight * 0.6f + jitter()

        path.quadraticTo(rightCurveTopX, rightCurveTopY, rightCurveEndX, rightCurveEndY)

        // Right side - straight line back to bottom point
        path.lineTo(startX + jitter(), startY + jitter())

        path.close()

        // Vary stroke width for each stroke to create texture
        val baseStrokeWidth = size * 0.06f
        val strokeWidth = baseStrokeWidth * (0.7f + random.nextFloat() * 0.6f) // 70-130% variation

        // Vary opacity for each stroke
        val strokeAlpha = color.alpha * (0.3f + random.nextFloat() * 0.7f) // 30-100% of base alpha

        // Draw the stroke with varied properties
        drawPath(
            path = path,
            color = color.copy(alpha = strokeAlpha),
            style = androidx.compose.ui.graphics.drawscope.Stroke(
                width = strokeWidth,
                cap = androidx.compose.ui.graphics.StrokeCap.Round,
                join = androidx.compose.ui.graphics.StrokeJoin.Round
            )
        )
    }
}

fun DrawScope.drawMangaSpeedLines(
    center: Offset,
    size: Float,
    color: Color,
    random: Random
) {
    // Create multiple overlapping diagonal lines for manga-style speed effect
    val numLines = 8 + random.nextInt(7) // 8-14 lines for dense, chaotic effect

    // Random base angle for this sketch group (creates directional movement)
    val baseAngle = random.nextFloat() * 2 * Math.PI.toFloat()

    for (lineIndex in 0 until numLines) {
        // Create diagonal lines with slight angle variations
        val angleVariation = (random.nextFloat() - 0.5f) * 0.8f // ±0.4 radians variation
        val lineAngle = baseAngle + angleVariation

        // Random line length within the sketch area
        val lineLength = size * (0.3f + random.nextFloat() * 0.7f) // 30-100% of sketch size

        // Random starting point within the sketch area
        val sketchRadius = size * 0.4f
        val startOffsetX = (random.nextFloat() - 0.5f) * sketchRadius
        val startOffsetY = (random.nextFloat() - 0.5f) * sketchRadius

        val startX = center.x + startOffsetX
        val startY = center.y + startOffsetY

        // Calculate end point based on angle and length
        val endX = startX + cos(lineAngle) * lineLength
        val endY = startY + sin(lineAngle) * lineLength

        // Vary stroke width for each line to create depth and movement
        val baseStrokeWidth = size * 0.008f // Thin base width
        val strokeWidth = baseStrokeWidth * (1f + random.nextFloat() * 4f) // 1x-5x variation for thick overlapping lines

        // Vary opacity for each line to create layering effect
        val strokeAlpha = color.alpha * (0.3f + random.nextFloat() * 0.7f) // 30-100% of base alpha

        // Draw the speed line
        drawLine(
            color = color.copy(alpha = strokeAlpha),
            start = Offset(startX, startY),
            end = Offset(endX, endY),
            strokeWidth = strokeWidth,
            cap = androidx.compose.ui.graphics.StrokeCap.Round
        )

        // Add some shorter overlapping lines for extra detail and chaos
        if (random.nextFloat() < 0.4f) { // 40% chance for additional short lines
            val shortLength = lineLength * (0.2f + random.nextFloat() * 0.3f) // 20-50% of main line
            val shortAngle = lineAngle + (random.nextFloat() - 0.5f) * 1.2f // More angle variation

            val shortStartX = startX + cos(lineAngle) * lineLength * 0.3f // Start partway along main line
            val shortStartY = startY + sin(lineAngle) * lineLength * 0.3f

            val shortEndX = shortStartX + cos(shortAngle) * shortLength
            val shortEndY = shortStartY + sin(shortAngle) * shortLength

            val shortStrokeWidth = strokeWidth * (0.5f + random.nextFloat() * 0.5f) // Thinner short lines

            drawLine(
                color = color.copy(alpha = strokeAlpha * 0.7f), // Slightly more transparent
                start = Offset(shortStartX, shortStartY),
                end = Offset(shortEndX, shortEndY),
                strokeWidth = shortStrokeWidth,
                cap = androidx.compose.ui.graphics.StrokeCap.Round
            )
        }
    }
}