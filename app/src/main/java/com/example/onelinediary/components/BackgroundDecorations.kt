package com.example.onelinediary.components

import android.util.Log
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.example.onelinediary.ui.theme.DecorationTheme
import com.example.onelinediary.ui.theme.LocalAppTheme
import com.example.onelinediary.components.getSoftPastelColor
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt
import kotlin.random.Random

// Data class to track placed decorations for collision detection
data class PlacedDecoration(
    val center: Offset,
    val size: Float
)

// Function to check if two decorations would overlap
fun decorationsOverlap(decoration1: PlacedDecoration, decoration2: PlacedDecoration, minSpacing: Float = 0f): Boolean {
    val distance = sqrt(
        (decoration1.center.x - decoration2.center.x) * (decoration1.center.x - decoration2.center.x) +
        (decoration1.center.y - decoration2.center.y) * (decoration1.center.y - decoration2.center.y)
    )
    val combinedRadius = (decoration1.size + decoration2.size) / 2f + minSpacing
    return distance < combinedRadius
}

@Composable
fun BackgroundDecorations(
    decorationTheme: DecorationTheme,
    modifier: Modifier = Modifier
) {
    Log.d("BackgroundDecorations", "Drawing decorations with theme: $decorationTheme")
    when (decorationTheme) {
        DecorationTheme.NONE -> {
            Log.d("BackgroundDecorations", "No decorations for theme: $decorationTheme")
            // No decorations
        }
        DecorationTheme.STANDARD -> {
            Log.d("BackgroundDecorations", "Drawing standard decorations")
            StandardDecorations(modifier = modifier)
        }
        DecorationTheme.ROMANTIC -> {
            Log.d("BackgroundDecorations", "Drawing romantic decorations")
            RomanticDecorations(modifier = modifier)
        }
    }
}

@Composable
fun TestDecorations(modifier: Modifier = Modifier) {
    Log.d("TestDecorations", "TestDecorations composable called")

    Canvas(modifier = modifier.fillMaxSize()) {
        Log.d("TestDecorations", "Canvas drawing - size: ${size.width} x ${size.height}")

        // Draw a big red circle in the center - impossible to miss!
        drawCircle(
            color = Color.Red,
            radius = 100f,
            center = Offset(size.width / 2, size.height / 2)
        )

        // Draw a blue rectangle in top-left
        drawRect(
            color = Color.Blue,
            topLeft = Offset(50f, 50f),
            size = Size(200f, 100f)
        )

        Log.d("TestDecorations", "Canvas drawing completed")
    }
}

@Composable
fun RomanticDecorations(modifier: Modifier = Modifier) {
    val density = LocalDensity.current
    val primaryColor = MaterialTheme.colorScheme.primary
    val onBackgroundColor = MaterialTheme.colorScheme.onBackground
    val softPastelColor = getSoftPastelColor() // Get the disabled button color

    Canvas(modifier = modifier.fillMaxSize()) {
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Create a seeded random for consistent decoration placement
        val random = Random(42) // Fixed seed for consistent placement

        // Use the exact same color tint as the chosen theme color for both flowers and hearts
        val decorationColor = primaryColor

        // List to track all placed decorations to avoid overlaps
        val placedDecorations = mutableListOf<PlacedDecoration>()
        val minSpacing = with(density) { 32.dp.toPx() } // Minimum spacing between decorations

        // Draw hand-drawn style hearts scattered across the background with collision detection
        var heartsPlaced = 0
        var attempts = 0
        val maxHearts = 30 // Reduced for better spacing with larger hearts
        val maxAttempts = 500

        while (heartsPlaced < maxHearts && attempts < maxAttempts) {
            attempts++

            // Random position across the entire canvas
            val x = random.nextFloat() * canvasWidth
            val y = random.nextFloat() * canvasHeight

            // Size variation for hand-drawn hearts (larger sizes for better visibility)
            val sizeCategory = random.nextFloat()
            val size = with(density) {
                when {
                    sizeCategory < 0.3f -> (20 + random.nextFloat() * 15).dp.toPx() // Small: 20-35dp
                    sizeCategory < 0.6f -> (40 + random.nextFloat() * 20).dp.toPx() // Medium: 40-60dp
                    else -> (65 + random.nextFloat() * 25).dp.toPx()               // Large: 65-90dp
                }
            }

            val newDecoration = PlacedDecoration(Offset(x, y), size)

            // Check if this position would overlap with any existing decorations
            val wouldOverlap = placedDecorations.any { existing ->
                decorationsOverlap(existing, newDecoration, minSpacing)
            }

            // Also check if decoration is too close to canvas edges
            val margin = size / 2f + minSpacing
            val tooCloseToEdge = x < margin || y < margin ||
                               x > canvasWidth - margin || y > canvasHeight - margin

            if (!wouldOverlap && !tooCloseToEdge) {
                // Safe to place this heart
                placedDecorations.add(newDecoration)
                heartsPlaced++

                // Use soft pastel color (disabled button color) with some transparency for artistic effect
                val alpha = 0.6f + random.nextFloat() * 0.3f // 60-90% opacity for bold effect

                drawHandDrawnHeart(
                    center = Offset(x, y),
                    size = size,
                    color = softPastelColor.copy(alpha = alpha),
                    random = random
                )
            }
        }
    }
}

@Composable
fun StandardDecorations(modifier: Modifier = Modifier) {
    val density = LocalDensity.current
    val primaryColor = MaterialTheme.colorScheme.primary // Get the enabled button color

    Canvas(modifier = modifier.fillMaxSize()) {
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Create a seeded random for consistent decoration placement
        val random = Random(123) // Different seed from romantic theme

        // Draw multiple diagonal lines covering the entire screen
        drawDiagonalLinesAcrossScreen(
            canvasWidth = canvasWidth,
            canvasHeight = canvasHeight,
            color = softPastelColor.copy(alpha = 0.25f), // Use disabled button color with transparency
            random = random
        )
    }
}

fun DrawScope.drawHandDrawnHeart(
    center: Offset,
    size: Float,
    color: Color,
    random: Random
) {
    // Create multiple paths for brushstroke effect
    val numStrokes = 3 + random.nextInt(3) // 3-5 strokes for texture

    for (strokeIndex in 0 until numStrokes) {
        val path = Path()

        // Add randomness for hand-drawn effect
        val jitterAmount = size * 0.02f
        val jitter = { random.nextFloat() * jitterAmount - jitterAmount / 2f }

        // Heart dimensions with slight variations
        val heartWidth = size * (1.0f + random.nextFloat() * 0.2f - 0.1f) // ±10% variation
        val heartHeight = size * (0.9f + random.nextFloat() * 0.2f - 0.1f) // ±10% variation

        // Bottom point with jitter
        val startX = center.x + jitter()
        val startY = center.y + heartHeight * 0.4f + jitter()

        path.moveTo(startX, startY)

        // Left side - straight line to left curve start
        val leftCurveStartX = startX - heartWidth * 0.3f + jitter()
        val leftCurveStartY = startY - heartHeight * 0.6f + jitter()
        path.lineTo(leftCurveStartX, leftCurveStartY)

        // Left top bump (first curve) - asymmetrical
        val leftCurveTopX = startX - heartWidth * 0.25f + jitter()
        val leftCurveTopY = startY - heartHeight * 0.9f + jitter()
        val leftCurveEndX = startX + jitter()
        val leftCurveEndY = startY - heartHeight * 0.7f + jitter()

        path.quadraticTo(leftCurveTopX, leftCurveTopY, leftCurveEndX, leftCurveEndY)

        // Right top bump (second curve) - slightly different for asymmetry
        val rightCurveTopX = startX + heartWidth * 0.28f + jitter()
        val rightCurveTopY = startY - heartHeight * 0.85f + jitter()
        val rightCurveEndX = startX + heartWidth * 0.3f + jitter()
        val rightCurveEndY = startY - heartHeight * 0.6f + jitter()

        path.quadraticTo(rightCurveTopX, rightCurveTopY, rightCurveEndX, rightCurveEndY)

        // Right side - straight line back to bottom point
        path.lineTo(startX + jitter(), startY + jitter())

        path.close()

        // Vary stroke width for each stroke to create texture
        val baseStrokeWidth = size * 0.06f
        val strokeWidth = baseStrokeWidth * (0.7f + random.nextFloat() * 0.6f) // 70-130% variation

        // Vary opacity for each stroke
        val strokeAlpha = color.alpha * (0.3f + random.nextFloat() * 0.7f) // 30-100% of base alpha

        // Draw the stroke with varied properties
        drawPath(
            path = path,
            color = color.copy(alpha = strokeAlpha),
            style = androidx.compose.ui.graphics.drawscope.Stroke(
                width = strokeWidth,
                cap = androidx.compose.ui.graphics.StrokeCap.Round,
                join = androidx.compose.ui.graphics.StrokeJoin.Round
            )
        )
    }
}

fun DrawScope.drawDiagonalLinesAcrossScreen(
    canvasWidth: Float,
    canvasHeight: Float,
    color: Color,
    random: Random
) {
    // Calculate the diagonal distance and spacing for lines
    val diagonalLength = sqrt(canvasWidth * canvasWidth + canvasHeight * canvasHeight)
    val lineSpacing = diagonalLength * 0.04f // 4% of diagonal for spacing between lines (doubled density)
    val numLines = (diagonalLength / lineSpacing).toInt() + 2 // Ensure full coverage

    // Base stroke width scaled to canvas size
    val baseStrokeWidth = (canvasWidth + canvasHeight) * 0.001f

    // Draw multiple diagonal lines covering the entire screen
    for (lineIndex in 0 until numLines) {
        val path = Path()

        // Calculate the starting position for this line
        // Lines go from bottom-left to top-right direction
        val offset = (lineIndex - numLines / 2) * lineSpacing

        // Calculate start and end points that ensure the line crosses the screen
        val startX: Float
        val startY: Float
        val endX: Float
        val endY: Float

        if (offset <= 0) {
            // Line starts from left edge or bottom edge
            if (offset >= -canvasHeight) {
                startX = 0f
                startY = canvasHeight + offset
            } else {
                startX = -(offset + canvasHeight)
                startY = 0f
            }
        } else {
            // Line starts from bottom edge
            startX = offset
            startY = canvasHeight
        }

        if (offset <= 0) {
            // Line ends at top edge or right edge
            if (offset >= -canvasHeight) {
                endX = canvasWidth + offset
                endY = 0f
            } else {
                endX = canvasWidth
                endY = -(offset + canvasHeight)
            }
        } else {
            // Line ends at right edge
            endX = canvasWidth
            endY = canvasHeight - offset
        }

        // Only draw lines that actually cross the visible screen
        if ((startX >= -lineSpacing && startX <= canvasWidth + lineSpacing) ||
            (endX >= -lineSpacing && endX <= canvasWidth + lineSpacing) ||
            (startY >= -lineSpacing && startY <= canvasHeight + lineSpacing) ||
            (endY >= -lineSpacing && endY <= canvasHeight + lineSpacing)) {

            path.moveTo(startX, startY)
            var currentY = startY

            // Create flowing curves along the diagonal line
            val numCurves = 4 + random.nextInt(3) // 4-6 curves for organic flow
            val totalDistance = sqrt((endX - startX) * (endX - startX) + (endY - startY) * (endY - startY))
            val segmentDistance = totalDistance / numCurves

            for (i in 1..numCurves) {
                val progress = i.toFloat() / numCurves
                val x = startX + (endX - startX) * progress
                val y = startY + (endY - startY) * progress

                // Add slight random variation to make lines organic
                val variation = lineSpacing * 0.15f * (random.nextFloat() - 0.5f)
                val variedX = x + variation * sin(Math.PI / 4).toFloat() // Perpendicular to diagonal
                val variedY = y - variation * cos(Math.PI / 4).toFloat()

                // Create control points for smooth curves
                val prevProgress = (i - 1).toFloat() / numCurves
                val prevX = startX + (endX - startX) * prevProgress
                val prevY = startY + (endY - startY) * prevProgress

                val controlDistance = segmentDistance * 0.3f
                val controlX1 = prevX + controlDistance * cos(Math.PI / 4).toFloat()
                val controlY1 = prevY - controlDistance * sin(Math.PI / 4).toFloat()
                val controlX2 = variedX - controlDistance * cos(Math.PI / 4).toFloat()
                val controlY2 = variedY + controlDistance * sin(Math.PI / 4).toFloat()

                path.cubicTo(controlX1, controlY1, controlX2, controlY2, variedX, variedY)
                currentY = variedY
            }

            // Vary stroke width and opacity for each line
            val strokeWidth = baseStrokeWidth * (1f + random.nextFloat() * 2f) // 1x-3x variation
            val alpha = color.alpha * (2.6f + random.nextFloat() * 2.4f) // 60-100% opacity variation (less transparent)

            drawPath(
                path = path,
                color = color.copy(alpha = alpha),
                style = androidx.compose.ui.graphics.drawscope.Stroke(
                    width = strokeWidth,
                    cap = androidx.compose.ui.graphics.StrokeCap.Round,
                    join = androidx.compose.ui.graphics.StrokeJoin.Round
                )
            )
        }
    }
}