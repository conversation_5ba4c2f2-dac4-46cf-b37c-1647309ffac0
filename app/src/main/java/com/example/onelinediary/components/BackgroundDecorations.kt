package com.example.onelinediary.components

import android.util.Log
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.example.onelinediary.ui.theme.DecorationTheme
import com.example.onelinediary.ui.theme.LocalAppTheme
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt
import kotlin.random.Random

// Data class to track placed decorations for collision detection
data class PlacedDecoration(
    val center: Offset,
    val size: Float
)

// Function to check if two decorations would overlap
fun decorationsOverlap(decoration1: PlacedDecoration, decoration2: PlacedDecoration, minSpacing: Float = 0f): Boolean {
    val distance = sqrt(
        (decoration1.center.x - decoration2.center.x) * (decoration1.center.x - decoration2.center.x) +
        (decoration1.center.y - decoration2.center.y) * (decoration1.center.y - decoration2.center.y)
    )
    val combinedRadius = (decoration1.size + decoration2.size) / 2f + minSpacing
    return distance < combinedRadius
}

@Composable
fun BackgroundDecorations(
    decorationTheme: DecorationTheme,
    modifier: Modifier = Modifier
) {
    Log.d("BackgroundDecorations", "Drawing decorations with theme: $decorationTheme")
    when (decorationTheme) {
        DecorationTheme.NONE, DecorationTheme.STANDARD -> {
            Log.d("BackgroundDecorations", "No decorations for theme: $decorationTheme")
            // No decorations
        }
        DecorationTheme.ROMANTIC -> {
            Log.d("BackgroundDecorations", "Drawing romantic decorations")
            RomanticDecorations(modifier = modifier)
        }
    }
}

@Composable
fun TestDecorations(modifier: Modifier = Modifier) {
    Log.d("TestDecorations", "TestDecorations composable called")

    Canvas(modifier = modifier.fillMaxSize()) {
        Log.d("TestDecorations", "Canvas drawing - size: ${size.width} x ${size.height}")

        // Draw a big red circle in the center - impossible to miss!
        drawCircle(
            color = Color.Red,
            radius = 100f,
            center = Offset(size.width / 2, size.height / 2)
        )

        // Draw a blue rectangle in top-left
        drawRect(
            color = Color.Blue,
            topLeft = Offset(50f, 50f),
            size = Size(200f, 100f)
        )

        Log.d("TestDecorations", "Canvas drawing completed")
    }
}

@Composable
fun RomanticDecorations(modifier: Modifier = Modifier) {
    val density = LocalDensity.current
    val primaryColor = MaterialTheme.colorScheme.primary
    val onBackgroundColor = MaterialTheme.colorScheme.onBackground

    Canvas(modifier = modifier.fillMaxSize()) {
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Create a seeded random for consistent decoration placement
        val random = Random(42) // Fixed seed for consistent placement

        // Use the exact same color tint as the chosen theme color for both flowers and hearts
        val decorationColor = primaryColor

        // List to track all placed decorations to avoid overlaps
        val placedDecorations = mutableListOf<PlacedDecoration>()
        val minSpacing = with(density) { 32.dp.toPx() } // Minimum spacing between decorations

        // Draw hand-drawn style hearts scattered across the background with collision detection
        var heartsPlaced = 0
        var attempts = 0
        val maxHearts = 20 // Reduced for better spacing with larger hearts
        val maxAttempts = 500

        while (heartsPlaced < maxHearts && attempts < maxAttempts) {
            attempts++

            // Random position across the entire canvas
            val x = random.nextFloat() * canvasWidth
            val y = random.nextFloat() * canvasHeight

            // Size variation for hand-drawn hearts (larger sizes for better visibility)
            val sizeCategory = random.nextFloat()
            val size = with(density) {
                when {
                    sizeCategory < 0.3f -> (20 + random.nextFloat() * 15).dp.toPx() // Small: 20-35dp
                    sizeCategory < 0.6f -> (40 + random.nextFloat() * 20).dp.toPx() // Medium: 40-60dp
                    else -> (65 + random.nextFloat() * 25).dp.toPx()               // Large: 65-90dp
                }
            }

            val newDecoration = PlacedDecoration(Offset(x, y), size)

            // Check if this position would overlap with any existing decorations
            val wouldOverlap = placedDecorations.any { existing ->
                decorationsOverlap(existing, newDecoration, minSpacing)
            }

            // Also check if decoration is too close to canvas edges
            val margin = size / 2f + minSpacing
            val tooCloseToEdge = x < margin || y < margin ||
                               x > canvasWidth - margin || y > canvasHeight - margin

            if (!wouldOverlap && !tooCloseToEdge) {
                // Safe to place this heart
                placedDecorations.add(newDecoration)
                heartsPlaced++

                // Use bold black color with some transparency for artistic effect
                val alpha = 0.6f + random.nextFloat() * 0.3f // 60-90% opacity for bold effect

                drawHandDrawnHeart(
                    center = Offset(x, y),
                    size = size,
                    color = Color.Black.copy(alpha = alpha),
                    random = random
                )
            }
        }
    }
}

fun DrawScope.drawHandDrawnHeart(
    center: Offset,
    size: Float,
    color: Color,
    random: Random
) {
    // Create multiple paths for brushstroke effect
    val numStrokes = 3 + random.nextInt(3) // 3-5 strokes for texture

    for (strokeIndex in 0 until numStrokes) {
        val path = Path()

        // Add randomness for hand-drawn effect
        val jitterAmount = size * 0.02f
        val jitter = { random.nextFloat() * jitterAmount - jitterAmount / 2f }

        // Heart dimensions with slight variations
        val heartWidth = size * (1.0f + random.nextFloat() * 0.2f - 0.1f) // ±10% variation
        val heartHeight = size * (0.9f + random.nextFloat() * 0.2f - 0.1f) // ±10% variation

        // Bottom point with jitter
        val startX = center.x + jitter()
        val startY = center.y + heartHeight * 0.4f + jitter()

        path.moveTo(startX, startY)

        // Left side of heart with organic curves and jitter
        path.cubicTo(
            startX - heartWidth * 0.6f + jitter(), startY - heartHeight * 0.3f + jitter(),
            startX - heartWidth * 0.6f + jitter(), startY - heartHeight * 0.9f + jitter(),
            startX - heartWidth * 0.3f + jitter(), startY - heartHeight * 0.7f + jitter()
        )

        // Left top bump (asymmetrical)
        path.cubicTo(
            startX - heartWidth * 0.15f + jitter(), startY - heartHeight * 0.9f + jitter(),
            startX - heartWidth * 0.05f + jitter(), startY - heartHeight * 0.9f + jitter(),
            startX + jitter(), startY - heartHeight * 0.7f + jitter()
        )

        // Right top bump (slightly different from left for asymmetry)
        path.cubicTo(
            startX + heartWidth * 0.05f + jitter(), startY - heartHeight * 0.85f + jitter(),
            startX + heartWidth * 0.18f + jitter(), startY - heartHeight * 0.95f + jitter(),
            startX + heartWidth * 0.32f + jitter(), startY - heartHeight * 0.65f + jitter()
        )

        // Right side of heart with different curve
        path.cubicTo(
            startX + heartWidth * 0.65f + jitter(), startY - heartHeight * 0.85f + jitter(),
            startX + heartWidth * 0.55f + jitter(), startY - heartHeight * 0.25f + jitter(),
            startX + jitter(), startY + jitter()
        )

        path.close()

        // Vary stroke width for each stroke to create texture
        val baseStrokeWidth = size * 0.06f
        val strokeWidth = baseStrokeWidth * (0.7f + random.nextFloat() * 0.6f) // 70-130% variation

        // Vary opacity for each stroke
        val strokeAlpha = color.alpha * (0.3f + random.nextFloat() * 0.7f) // 30-100% of base alpha

        // Draw the stroke with varied properties
        drawPath(
            path = path,
            color = color.copy(alpha = strokeAlpha),
            style = androidx.compose.ui.graphics.drawscope.Stroke(
                width = strokeWidth,
                cap = androidx.compose.ui.graphics.StrokeCap.Round,
                join = androidx.compose.ui.graphics.StrokeJoin.Round
            )
        )
    }
}

fun DrawScope.drawCartoonHeart(
    center: Offset,
    size: Float,
    color: Color
) {
    val strokeWidth = size * 0.04f // Thinner outline width (reduced from 0.08f)
    val path = Path()

    // Clean, stylized heart shape with bold outlines
    val heartWidth = size * 1.2f
    val heartHeight = size

    val startX = center.x
    val startY = center.y + heartHeight * 0.4f // Bottom point

    path.moveTo(startX, startY)

    // Left side of heart
    path.cubicTo(
        startX - heartWidth * 0.6f, startY - heartHeight * 0.3f,
        startX - heartWidth * 0.6f, startY - heartHeight * 0.9f,
        startX - heartWidth * 0.3f, startY - heartHeight * 0.7f
    )

    // Left top bump (more rounded)
    path.cubicTo(
        startX - heartWidth * 0.15f, startY - heartHeight * 0.9f,
        startX - heartWidth * 0.05f, startY - heartHeight * 0.9f,
        startX, startY - heartHeight * 0.7f
    )

    // Right top bump (more rounded)
    path.cubicTo(
        startX + heartWidth * 0.05f, startY - heartHeight * 0.9f,
        startX + heartWidth * 0.15f, startY - heartHeight * 0.9f,
        startX + heartWidth * 0.3f, startY - heartHeight * 0.7f
    )

    // Right side of heart
    path.cubicTo(
        startX + heartWidth * 0.6f, startY - heartHeight * 0.9f,
        startX + heartWidth * 0.6f, startY - heartHeight * 0.3f,
        startX, startY
    )

    path.close()

    // Draw heart with bold outline only (line art style)
    drawPath(
        path = path,
        color = color,
        style = androidx.compose.ui.graphics.drawscope.Stroke(width = strokeWidth)
    )
}
