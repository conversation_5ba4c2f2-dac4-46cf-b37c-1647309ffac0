package com.example.onelinediary.components

import android.util.Log
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.example.onelinediary.ui.theme.DecorationTheme
import com.example.onelinediary.ui.theme.LocalAppTheme
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

@Composable
fun BackgroundDecorations(
    decorationTheme: DecorationTheme,
    modifier: Modifier = Modifier
) {
    Log.d("BackgroundDecorations", "Drawing decorations with theme: $decorationTheme")
    when (decorationTheme) {
        DecorationTheme.NONE, DecorationTheme.STANDARD -> {
            Log.d("BackgroundDecorations", "No decorations for theme: $decorationTheme")
            // No decorations
        }
        DecorationTheme.ROMANTIC -> {
            Log.d("BackgroundDecorations", "Drawing romantic decorations")
            RomanticDecorations(modifier = modifier)
        }
    }
}

@Composable
fun TestDecorations(modifier: Modifier = Modifier) {
    Log.d("TestDecorations", "TestDecorations composable called")

    Canvas(modifier = modifier.fillMaxSize()) {
        Log.d("TestDecorations", "Canvas drawing - size: ${size.width} x ${size.height}")

        // Draw a big red circle in the center - impossible to miss!
        drawCircle(
            color = Color.Red,
            radius = 100f,
            center = Offset(size.width / 2, size.height / 2)
        )

        // Draw a blue rectangle in top-left
        drawRect(
            color = Color.Blue,
            topLeft = Offset(50f, 50f),
            size = Size(200f, 100f)
        )

        Log.d("TestDecorations", "Canvas drawing completed")
    }
}

@Composable
fun RomanticDecorations(modifier: Modifier = Modifier) {
    val density = LocalDensity.current
    val primaryColor = MaterialTheme.colorScheme.primary
    val onBackgroundColor = MaterialTheme.colorScheme.onBackground

    Canvas(modifier = modifier.fillMaxSize()) {
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Create a seeded random for consistent decoration placement
        val random = Random(42) // Fixed seed for consistent placement

        // Use the exact same color tint as the chosen theme color for both flowers and hearts
        val decorationColor = primaryColor

        // Draw realistic flowers scattered across the background with much bigger sizes and extreme variation
        // Double the amount and ensure better spacing
        repeat(24) { index ->
            // Create a grid-based placement with some randomness to ensure better spacing
            val gridCols = 6
            val gridRows = 4
            val gridX = index % gridCols
            val gridY = index / gridCols

            val cellWidth = canvasWidth / gridCols
            val cellHeight = canvasHeight / gridRows

            // Add randomness within each grid cell for natural look
            val x = gridX * cellWidth + random.nextFloat() * cellWidth
            val y = gridY * cellHeight + random.nextFloat() * cellHeight
            // Extreme size variation from tiny to very large flowers
            val sizeCategory = random.nextFloat()
            val size = with(density) {
                when {
                    sizeCategory < 0.15f -> (6 + random.nextFloat() * 8).dp.toPx()   // Tiny: 6-14dp
                    sizeCategory < 0.35f -> (15 + random.nextFloat() * 15).dp.toPx() // Small: 15-30dp
                    sizeCategory < 0.65f -> (32 + random.nextFloat() * 20).dp.toPx() // Medium: 32-52dp
                    sizeCategory < 0.85f -> (55 + random.nextFloat() * 25).dp.toPx() // Large: 55-80dp
                    else -> (85 + random.nextFloat() * 35).dp.toPx()                 // Huge: 85-120dp
                }
            }
            val alpha = 0.12f + random.nextFloat() * 0.18f // Lighter: 12-30% opacity

            drawRealisticFlower(
                center = Offset(x, y),
                size = size,
                color = decorationColor.copy(alpha = alpha), // Exact same color tint
                petalCount = 5 + random.nextInt(3) // 5-7 petals for variety
            )
        }

        // Draw cartoon hearts scattered across the background with extreme size variation
        // Double the amount and ensure better spacing
        repeat(20) { index ->
            // Create a grid-based placement with some randomness to ensure better spacing
            val gridCols = 5
            val gridRows = 4
            val gridX = index % gridCols
            val gridY = index / gridCols

            val cellWidth = canvasWidth / gridCols
            val cellHeight = canvasHeight / gridRows

            // Add randomness within each grid cell for natural look
            val x = gridX * cellWidth + random.nextFloat() * cellWidth
            val y = gridY * cellHeight + random.nextFloat() * cellHeight
            // Extreme size variation from tiny to large hearts
            val sizeCategory = random.nextFloat()
            val size = with(density) {
                when {
                    sizeCategory < 0.2f -> (4 + random.nextFloat() * 6).dp.toPx()   // Tiny: 4-10dp
                    sizeCategory < 0.4f -> (12 + random.nextFloat() * 10).dp.toPx() // Small: 12-22dp
                    sizeCategory < 0.7f -> (25 + random.nextFloat() * 15).dp.toPx() // Medium: 25-40dp
                    sizeCategory < 0.9f -> (45 + random.nextFloat() * 20).dp.toPx() // Large: 45-65dp
                    else -> (70 + random.nextFloat() * 25).dp.toPx()                // Huge: 70-95dp
                }
            }
            val alpha = 0.1f + random.nextFloat() * 0.2f // Lighter: 10-30% opacity

            drawCartoonHeart(
                center = Offset(x, y),
                size = size,
                color = decorationColor.copy(alpha = alpha) // Exact same color tint as flowers
            )
        }
    }
}

fun DrawScope.drawRealisticFlower(
    center: Offset,
    size: Float,
    color: Color,
    petalCount: Int = 5
) {
    val strokeWidth = size * 0.08f // Bold outline width
    val petalRadius = size * 0.35f
    val angleStep = (2 * Math.PI / petalCount).toFloat()

    // Draw curved stem
    val stemPath = Path()
    val stemStart = Offset(center.x, center.y + size * 0.4f)
    val stemEnd = Offset(center.x - size * 0.1f, center.y + size * 0.8f)
    val stemControl = Offset(center.x - size * 0.3f, center.y + size * 0.6f)

    stemPath.moveTo(stemStart.x, stemStart.y)
    stemPath.quadraticTo(stemControl.x, stemControl.y, stemEnd.x, stemEnd.y)

    drawPath(
        path = stemPath,
        color = color,
        style = androidx.compose.ui.graphics.drawscope.Stroke(width = strokeWidth * 0.7f)
    )

    // Draw leaves on stem
    val leafSize = size * 0.2f
    val leaf1Center = Offset(center.x - size * 0.15f, center.y + size * 0.5f)
    val leaf2Center = Offset(center.x - size * 0.25f, center.y + size * 0.65f)

    // Draw stylized leaves
    for (leafCenter in listOf(leaf1Center, leaf2Center)) {
        val leafPath = Path()
        leafPath.moveTo(leafCenter.x, leafCenter.y - leafSize)
        leafPath.quadraticTo(leafCenter.x + leafSize * 0.8f, leafCenter.y, leafCenter.x, leafCenter.y + leafSize)
        leafPath.quadraticTo(leafCenter.x - leafSize * 0.8f, leafCenter.y, leafCenter.x, leafCenter.y - leafSize)
        leafPath.close()

        drawPath(
            path = leafPath,
            color = color,
            style = androidx.compose.ui.graphics.drawscope.Stroke(width = strokeWidth * 0.6f)
        )
    }

    // Draw rounded petals with bold outlines
    for (i in 0 until petalCount) {
        val angle = i * angleStep
        val petalCenter = Offset(
            center.x + cos(angle) * size * 0.25f,
            center.y + sin(angle) * size * 0.25f
        )

        // Draw petal as circle with bold outline
        drawCircle(
            color = color,
            radius = petalRadius,
            center = petalCenter,
            style = androidx.compose.ui.graphics.drawscope.Stroke(width = strokeWidth)
        )
    }

    // Draw flower center with bold outline
    drawCircle(
        color = color,
        radius = size * 0.12f,
        center = center,
        style = androidx.compose.ui.graphics.drawscope.Stroke(width = strokeWidth)
    )

    // Add small center dot
    drawCircle(
        color = color,
        radius = size * 0.05f,
        center = center
    )
}

fun DrawScope.drawCartoonHeart(
    center: Offset,
    size: Float,
    color: Color
) {
    val strokeWidth = size * 0.08f // Bold outline width
    val path = Path()

    // Clean, stylized heart shape with bold outlines
    val heartWidth = size * 1.2f
    val heartHeight = size

    val startX = center.x
    val startY = center.y + heartHeight * 0.4f // Bottom point

    path.moveTo(startX, startY)

    // Left side of heart
    path.cubicTo(
        startX - heartWidth * 0.6f, startY - heartHeight * 0.3f,
        startX - heartWidth * 0.6f, startY - heartHeight * 0.9f,
        startX - heartWidth * 0.3f, startY - heartHeight * 0.7f
    )

    // Left top bump (more rounded)
    path.cubicTo(
        startX - heartWidth * 0.15f, startY - heartHeight * 0.9f,
        startX - heartWidth * 0.05f, startY - heartHeight * 0.9f,
        startX, startY - heartHeight * 0.7f
    )

    // Right top bump (more rounded)
    path.cubicTo(
        startX + heartWidth * 0.05f, startY - heartHeight * 0.9f,
        startX + heartWidth * 0.15f, startY - heartHeight * 0.9f,
        startX + heartWidth * 0.3f, startY - heartHeight * 0.7f
    )

    // Right side of heart
    path.cubicTo(
        startX + heartWidth * 0.6f, startY - heartHeight * 0.9f,
        startX + heartWidth * 0.6f, startY - heartHeight * 0.3f,
        startX, startY
    )

    path.close()

    // Draw heart with bold outline only (line art style)
    drawPath(
        path = path,
        color = color,
        style = androidx.compose.ui.graphics.drawscope.Stroke(width = strokeWidth)
    )
}
