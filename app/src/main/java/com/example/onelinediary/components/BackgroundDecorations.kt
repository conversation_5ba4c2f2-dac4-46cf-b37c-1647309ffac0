package com.example.onelinediary.components

import android.util.Log
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.example.onelinediary.ui.theme.DecorationTheme
import com.example.onelinediary.ui.theme.LocalAppTheme
import com.example.onelinediary.components.getSoftPastelColor
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt
import kotlin.random.Random

// Data class to track placed decorations for collision detection
data class PlacedDecoration(
    val center: Offset,
    val size: Float
)

// Function to check if two decorations would overlap
fun decorationsOverlap(decoration1: PlacedDecoration, decoration2: PlacedDecoration, minSpacing: Float = 0f): Boolean {
    val distance = sqrt(
        (decoration1.center.x - decoration2.center.x) * (decoration1.center.x - decoration2.center.x) +
        (decoration1.center.y - decoration2.center.y) * (decoration1.center.y - decoration2.center.y)
    )
    val combinedRadius = (decoration1.size + decoration2.size) / 2f + minSpacing
    return distance < combinedRadius
}

@Composable
fun BackgroundDecorations(
    decorationTheme: DecorationTheme,
    modifier: Modifier = Modifier
) {
    Log.d("BackgroundDecorations", "Drawing decorations with theme: $decorationTheme")
    when (decorationTheme) {
        DecorationTheme.NONE -> {
            Log.d("BackgroundDecorations", "No decorations for theme: $decorationTheme")
            // No decorations
        }
        DecorationTheme.STANDARD -> {
            Log.d("BackgroundDecorations", "Drawing standard decorations")
            StandardDecorations(modifier = modifier)
        }
        DecorationTheme.ROMANTIC -> {
            Log.d("BackgroundDecorations", "Drawing romantic decorations")
            RomanticDecorations(modifier = modifier)
        }
    }
}

@Composable
fun TestDecorations(modifier: Modifier = Modifier) {
    Log.d("TestDecorations", "TestDecorations composable called")

    Canvas(modifier = modifier.fillMaxSize()) {
        Log.d("TestDecorations", "Canvas drawing - size: ${size.width} x ${size.height}")

        // Draw a big red circle in the center - impossible to miss!
        drawCircle(
            color = Color.Red,
            radius = 100f,
            center = Offset(size.width / 2, size.height / 2)
        )

        // Draw a blue rectangle in top-left
        drawRect(
            color = Color.Blue,
            topLeft = Offset(50f, 50f),
            size = Size(200f, 100f)
        )

        Log.d("TestDecorations", "Canvas drawing completed")
    }
}

@Composable
fun RomanticDecorations(modifier: Modifier = Modifier) {
    val density = LocalDensity.current
    val primaryColor = MaterialTheme.colorScheme.primary
    val onBackgroundColor = MaterialTheme.colorScheme.onBackground
    val softPastelColor = getSoftPastelColor() // Get the disabled button color

    Canvas(modifier = modifier.fillMaxSize()) {
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Create a seeded random for consistent decoration placement
        val random = Random(42) // Fixed seed for consistent placement

        // Use the exact same color tint as the chosen theme color for both flowers and hearts
        val decorationColor = primaryColor

        // List to track all placed decorations to avoid overlaps
        val placedDecorations = mutableListOf<PlacedDecoration>()
        val minSpacing = with(density) { 32.dp.toPx() } // Minimum spacing between decorations

        // Draw hand-drawn style hearts scattered across the background with collision detection
        var heartsPlaced = 0
        var attempts = 0
        val maxHearts = 30 // Reduced for better spacing with larger hearts
        val maxAttempts = 500

        while (heartsPlaced < maxHearts && attempts < maxAttempts) {
            attempts++

            // Random position across the entire canvas
            val x = random.nextFloat() * canvasWidth
            val y = random.nextFloat() * canvasHeight

            // Size variation for hand-drawn hearts (larger sizes for better visibility)
            val sizeCategory = random.nextFloat()
            val size = with(density) {
                when {
                    sizeCategory < 0.3f -> (20 + random.nextFloat() * 15).dp.toPx() // Small: 20-35dp
                    sizeCategory < 0.6f -> (40 + random.nextFloat() * 20).dp.toPx() // Medium: 40-60dp
                    else -> (65 + random.nextFloat() * 25).dp.toPx()               // Large: 65-90dp
                }
            }

            val newDecoration = PlacedDecoration(Offset(x, y), size)

            // Check if this position would overlap with any existing decorations
            val wouldOverlap = placedDecorations.any { existing ->
                decorationsOverlap(existing, newDecoration, minSpacing)
            }

            // Also check if decoration is too close to canvas edges
            val margin = size / 2f + minSpacing
            val tooCloseToEdge = x < margin || y < margin ||
                               x > canvasWidth - margin || y > canvasHeight - margin

            if (!wouldOverlap && !tooCloseToEdge) {
                // Safe to place this heart
                placedDecorations.add(newDecoration)
                heartsPlaced++

                // Use soft pastel color (disabled button color) with some transparency for artistic effect
                val alpha = 0.6f + random.nextFloat() * 0.3f // 60-90% opacity for bold effect

                drawHandDrawnHeart(
                    center = Offset(x, y),
                    size = size,
                    color = softPastelColor.copy(alpha = alpha),
                    random = random
                )
            }
        }
    }
}

@Composable
fun StandardDecorations(modifier: Modifier = Modifier) {
    val density = LocalDensity.current

    Canvas(modifier = modifier.fillMaxSize()) {
        val canvasWidth = size.width
        val canvasHeight = size.height

        // Create a seeded random for consistent decoration placement
        val random = Random(123) // Different seed from romantic theme

        // Draw a single continuous line sketch that stretches across the entire background
        drawContinuousLineSketch(
            canvasWidth = canvasWidth,
            canvasHeight = canvasHeight,
            color = Color.Black.copy(alpha = 0.12f), // Subtle transparency
            random = random
        )
    }
}

fun DrawScope.drawHandDrawnHeart(
    center: Offset,
    size: Float,
    color: Color,
    random: Random
) {
    // Create multiple paths for brushstroke effect
    val numStrokes = 3 + random.nextInt(3) // 3-5 strokes for texture

    for (strokeIndex in 0 until numStrokes) {
        val path = Path()

        // Add randomness for hand-drawn effect
        val jitterAmount = size * 0.02f
        val jitter = { random.nextFloat() * jitterAmount - jitterAmount / 2f }

        // Heart dimensions with slight variations
        val heartWidth = size * (1.0f + random.nextFloat() * 0.2f - 0.1f) // ±10% variation
        val heartHeight = size * (0.9f + random.nextFloat() * 0.2f - 0.1f) // ±10% variation

        // Bottom point with jitter
        val startX = center.x + jitter()
        val startY = center.y + heartHeight * 0.4f + jitter()

        path.moveTo(startX, startY)

        // Left side - straight line to left curve start
        val leftCurveStartX = startX - heartWidth * 0.3f + jitter()
        val leftCurveStartY = startY - heartHeight * 0.6f + jitter()
        path.lineTo(leftCurveStartX, leftCurveStartY)

        // Left top bump (first curve) - asymmetrical
        val leftCurveTopX = startX - heartWidth * 0.25f + jitter()
        val leftCurveTopY = startY - heartHeight * 0.9f + jitter()
        val leftCurveEndX = startX + jitter()
        val leftCurveEndY = startY - heartHeight * 0.7f + jitter()

        path.quadraticTo(leftCurveTopX, leftCurveTopY, leftCurveEndX, leftCurveEndY)

        // Right top bump (second curve) - slightly different for asymmetry
        val rightCurveTopX = startX + heartWidth * 0.28f + jitter()
        val rightCurveTopY = startY - heartHeight * 0.85f + jitter()
        val rightCurveEndX = startX + heartWidth * 0.3f + jitter()
        val rightCurveEndY = startY - heartHeight * 0.6f + jitter()

        path.quadraticTo(rightCurveTopX, rightCurveTopY, rightCurveEndX, rightCurveEndY)

        // Right side - straight line back to bottom point
        path.lineTo(startX + jitter(), startY + jitter())

        path.close()

        // Vary stroke width for each stroke to create texture
        val baseStrokeWidth = size * 0.06f
        val strokeWidth = baseStrokeWidth * (0.7f + random.nextFloat() * 0.6f) // 70-130% variation

        // Vary opacity for each stroke
        val strokeAlpha = color.alpha * (0.3f + random.nextFloat() * 0.7f) // 30-100% of base alpha

        // Draw the stroke with varied properties
        drawPath(
            path = path,
            color = color.copy(alpha = strokeAlpha),
            style = androidx.compose.ui.graphics.drawscope.Stroke(
                width = strokeWidth,
                cap = androidx.compose.ui.graphics.StrokeCap.Round,
                join = androidx.compose.ui.graphics.StrokeJoin.Round
            )
        )
    }
}

fun DrawScope.drawContinuousLineSketch(
    canvasWidth: Float,
    canvasHeight: Float,
    color: Color,
    random: Random
) {
    val path = Path()

    // Create a continuous line that flows across the entire background
    // Start from one edge and create a flowing, organic path to the other side

    // Determine if we start from left/right or top/bottom
    val startFromSide = random.nextBoolean()

    if (startFromSide) {
        // Start from left edge, flow to right edge
        val startY = canvasHeight * (0.2f + random.nextFloat() * 0.6f) // Start somewhere in middle area
        val endY = canvasHeight * (0.2f + random.nextFloat() * 0.6f)   // End somewhere in middle area

        path.moveTo(0f, startY)
        var currentY = startY // Track current position manually

        // Create flowing curves across the width
        val numCurves = 4 + random.nextInt(4) // 4-7 curves for organic flow
        val segmentWidth = canvasWidth / numCurves

        for (i in 1..numCurves) {
            val x = i * segmentWidth
            val y = if (i == numCurves) {
                endY // Final point
            } else {
                // Interpolate between start and end with random variation
                val progress = i.toFloat() / numCurves
                val baseY = startY + (endY - startY) * progress
                val variation = canvasHeight * 0.15f * (random.nextFloat() - 0.5f) // ±15% height variation
                (baseY + variation).coerceIn(canvasHeight * 0.1f, canvasHeight * 0.9f)
            }

            // Create control points for smooth curves
            val prevX = (i - 1) * segmentWidth
            val controlX1 = prevX + segmentWidth * 0.3f
            val controlX2 = prevX + segmentWidth * 0.7f
            val controlY1 = currentY + canvasHeight * 0.1f * (random.nextFloat() - 0.5f)
            val controlY2 = y + canvasHeight * 0.1f * (random.nextFloat() - 0.5f)

            path.cubicTo(controlX1, controlY1, controlX2, controlY2, x, y)
            currentY = y // Update current position
        }
    } else {
        // Start from top edge, flow to bottom edge
        val startX = canvasWidth * (0.2f + random.nextFloat() * 0.6f)  // Start somewhere in middle area
        val endX = canvasWidth * (0.2f + random.nextFloat() * 0.6f)    // End somewhere in middle area

        path.moveTo(startX, 0f)
        var currentX = startX // Track current position manually

        // Create flowing curves across the height
        val numCurves = 4 + random.nextInt(4) // 4-7 curves for organic flow
        val segmentHeight = canvasHeight / numCurves

        for (i in 1..numCurves) {
            val y = i * segmentHeight
            val x = if (i == numCurves) {
                endX // Final point
            } else {
                // Interpolate between start and end with random variation
                val progress = i.toFloat() / numCurves
                val baseX = startX + (endX - startX) * progress
                val variation = canvasWidth * 0.15f * (random.nextFloat() - 0.5f) // ±15% width variation
                (baseX + variation).coerceIn(canvasWidth * 0.1f, canvasWidth * 0.9f)
            }

            // Create control points for smooth curves
            val prevY = (i - 1) * segmentHeight
            val controlY1 = prevY + segmentHeight * 0.3f
            val controlY2 = prevY + segmentHeight * 0.7f
            val controlX1 = currentX + canvasWidth * 0.1f * (random.nextFloat() - 0.5f)
            val controlX2 = x + canvasWidth * 0.1f * (random.nextFloat() - 0.5f)

            path.cubicTo(controlX1, controlY1, controlX2, controlY2, x, y)
            currentX = x // Update current position
        }
    }

    // Draw the main continuous line with varying thickness
    val baseStrokeWidth = (canvasWidth + canvasHeight) * 0.002f // Scale with canvas size
    val strokeWidth = baseStrokeWidth * (2f + random.nextFloat() * 3f) // 2x-5x variation for thick line

    drawPath(
        path = path,
        color = color,
        style = androidx.compose.ui.graphics.drawscope.Stroke(
            width = strokeWidth,
            cap = androidx.compose.ui.graphics.StrokeCap.Round,
            join = androidx.compose.ui.graphics.StrokeJoin.Round
        )
    )

    // Add some parallel lines for sketch-like detail
    val numParallelLines = 2 + random.nextInt(3) // 2-4 parallel lines

    for (parallelIndex in 1..numParallelLines) {
        val parallelPath = Path()
        val offset = baseStrokeWidth * (3f + random.nextFloat() * 4f) * if (random.nextBoolean()) 1f else -1f

        // Create a parallel path by offsetting the original path
        // This is a simplified approach - we'll create a similar but offset path
        if (startFromSide) {
            val startY = canvasHeight * (0.2f + random.nextFloat() * 0.6f)
            val endY = canvasHeight * (0.2f + random.nextFloat() * 0.6f)

            parallelPath.moveTo(0f, startY + offset)
            var parallelCurrentY = startY + offset // Track parallel path position

            val numCurves = 4 + random.nextInt(4)
            val segmentWidth = canvasWidth / numCurves

            for (i in 1..numCurves) {
                val x = i * segmentWidth
                val y = if (i == numCurves) {
                    endY + offset
                } else {
                    val progress = i.toFloat() / numCurves
                    val baseY = startY + (endY - startY) * progress
                    val variation = canvasHeight * 0.1f * (random.nextFloat() - 0.5f)
                    (baseY + variation + offset).coerceIn(canvasHeight * 0.05f, canvasHeight * 0.95f)
                }

                val prevX = (i - 1) * segmentWidth
                val controlX1 = prevX + segmentWidth * 0.3f
                val controlX2 = prevX + segmentWidth * 0.7f
                val controlY1 = parallelCurrentY + canvasHeight * 0.08f * (random.nextFloat() - 0.5f)
                val controlY2 = y + canvasHeight * 0.08f * (random.nextFloat() - 0.5f)

                parallelPath.cubicTo(controlX1, controlY1, controlX2, controlY2, x, y)
                parallelCurrentY = y // Update parallel current position
            }
        } else {
            val startX = canvasWidth * (0.2f + random.nextFloat() * 0.6f)
            val endX = canvasWidth * (0.2f + random.nextFloat() * 0.6f)

            parallelPath.moveTo(startX + offset, 0f)
            var parallelCurrentX = startX + offset // Track parallel path position

            val numCurves = 4 + random.nextInt(4)
            val segmentHeight = canvasHeight / numCurves

            for (i in 1..numCurves) {
                val y = i * segmentHeight
                val x = if (i == numCurves) {
                    endX + offset
                } else {
                    val progress = i.toFloat() / numCurves
                    val baseX = startX + (endX - startX) * progress
                    val variation = canvasWidth * 0.1f * (random.nextFloat() - 0.5f)
                    (baseX + variation + offset).coerceIn(canvasWidth * 0.05f, canvasWidth * 0.95f)
                }

                val prevY = (i - 1) * segmentHeight
                val controlY1 = prevY + segmentHeight * 0.3f
                val controlY2 = prevY + segmentHeight * 0.7f
                val controlX1 = parallelCurrentX + canvasWidth * 0.08f * (random.nextFloat() - 0.5f)
                val controlX2 = x + canvasWidth * 0.08f * (random.nextFloat() - 0.5f)

                parallelPath.cubicTo(controlX1, controlY1, controlX2, controlY2, x, y)
                parallelCurrentX = x // Update parallel current position
            }
        }

        // Draw parallel line with thinner stroke and lower opacity
        val parallelStrokeWidth = strokeWidth * (0.3f + random.nextFloat() * 0.4f) // 30-70% of main line
        val parallelAlpha = color.alpha * (0.4f + random.nextFloat() * 0.4f) // 40-80% of main alpha

        drawPath(
            path = parallelPath,
            color = color.copy(alpha = parallelAlpha),
            style = androidx.compose.ui.graphics.drawscope.Stroke(
                width = parallelStrokeWidth,
                cap = androidx.compose.ui.graphics.StrokeCap.Round,
                join = androidx.compose.ui.graphics.StrokeJoin.Round
            )
        )
    }
}