package com.example.onelinediary

import android.os.Environment
import android.util.Log
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowLeft
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import com.example.onelinediary.components.standardOutlinedButtonColors
import com.example.onelinediary.components.standardButtonModifier
import com.example.onelinediary.components.standardButtonPadding
import com.example.onelinediary.components.standardButtonShape
import com.example.onelinediary.components.standardButtonColors
import com.example.onelinediary.components.backButtonModifier
import com.example.onelinediary.stringResource
import com.example.onelinediary.components.backButtonColors
import com.example.onelinediary.ui.theme.ButtonGreen

@Composable
fun HistoryScreen(
    onBack: () -> Unit,
    onDaySelected: (Calendar) -> Unit,
    saveDateManager: SaveDateManager
) {
    val context = LocalContext.current
    var currentDate by remember { mutableStateOf(Calendar.getInstance()) }

    // Get all dates with content for debugging
    val allDates = remember(saveDateManager) { saveDateManager.getAllSaveDates() }

    // Log the dates for debugging
    LaunchedEffect(Unit) {
        Log.d("HistoryScreen", "All dates with content: $allDates")

        // Force a scan of the file system to rebuild the dates list if it's empty
        if (allDates.isEmpty()) {
            Log.d("HistoryScreen", "No dates found, forcing a scan of the file system")
            // This will trigger the scanFileSystemForDates method
            val updatedDates = saveDateManager.getAllSaveDates()
            Log.d("HistoryScreen", "After scan, found dates: $updatedDates")
        }
    }

    // Get the background color from the theme
    val backgroundColor = MaterialTheme.colorScheme.background

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(backgroundColor)
            .padding(16.dp),
        verticalArrangement = Arrangement.Top
    ) {
        // Back button
        Button(
            onClick = onBack,
            modifier = backButtonModifier(Modifier.align(Alignment.Start)),
            contentPadding = standardButtonPadding,
            colors = standardButtonColors(),
            shape = standardButtonShape
        ) {
            Icon(
                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                contentDescription = "Back",
                tint = MaterialTheme.colorScheme.onBackground
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Calendar view
        CalendarView(
            currentDate = currentDate,
            onPreviousMonth = {
                val newDate = currentDate.clone() as Calendar
                newDate.add(Calendar.MONTH, -1)
                currentDate = newDate
            },
            onNextMonth = {
                val newDate = currentDate.clone() as Calendar
                newDate.add(Calendar.MONTH, 1)
                currentDate = newDate
            },
            onDaySelected = onDaySelected,
            saveDateManager = saveDateManager
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Mood Legend
        Column(
            modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = stringResource(R.string.mood_legend),
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onBackground,
                modifier = Modifier.padding(bottom = 8.dp)
            )

            // Mood legend items in horizontal rows
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // First row - Very Sad, Sad, Neutral
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    MoodLegendItem(
                        color = Color(0xFFE57373),
                        text = stringResource(R.string.very_sad),
                        modifier = Modifier.weight(1f)
                    )
                    MoodLegendItem(
                        color = Color(0xFFFFB74D),
                        text = stringResource(R.string.sad),
                        modifier = Modifier.weight(1f)
                    )
                    MoodLegendItem(
                        color = Color(0xFFFFEE58),
                        text = stringResource(R.string.neutral),
                        modifier = Modifier.weight(1f)
                    )
                }

                // Second row - Happy, Very Happy
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    MoodLegendItem(
                        color = Color(0xFFAED581),
                        text = stringResource(R.string.happy),
                        modifier = Modifier.weight(1f)
                    )
                    MoodLegendItem(
                        color = Color(0xFF2E7D32),
                        text = stringResource(R.string.very_happy),
                        modifier = Modifier.weight(1f)
                    )
                    // Empty space to balance the row
                    Spacer(modifier = Modifier.weight(1f))
                }
            }

            // Mood legend description
            Text(
                text = stringResource(R.string.mood_legend_description),
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
                modifier = Modifier.padding(top = 8.dp)
            )
        }



        // Debug information - show all dates with content
        if (allDates.isEmpty()) {
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "No diary entries found. Add some entries to see them here!",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.error,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }
    }
}

@Composable
fun CalendarView(
    currentDate: Calendar,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit,
    onDaySelected: (Calendar) -> Unit,
    saveDateManager: SaveDateManager
) {
    val monthYearFormatter = SimpleDateFormat("MMMM yyyy", Locale.getDefault())
    val monthYearText = monthYearFormatter.format(currentDate.time)

    Column(modifier = Modifier.fillMaxWidth()) {
        // Month and year with navigation buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onPreviousMonth) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowLeft,
                    contentDescription = "Previous Month"
                )
            }

            Text(
                text = monthYearText,
                style = MaterialTheme.typography.titleLarge,
                fontWeight = FontWeight.Bold
            )

            IconButton(onClick = onNextMonth) {
                Icon(
                    imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                    contentDescription = "Next Month"
                )
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        // Day of week headers
        Row(modifier = Modifier.fillMaxWidth()) {
            val daysOfWeek = listOf("S", "M", "T", "W", "T", "F", "S")
            daysOfWeek.forEach { day ->
                Text(
                    text = day,
                    modifier = Modifier
                        .weight(1f)
                        .padding(4.dp),
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }

        Spacer(modifier = Modifier.height(4.dp))

        // Calendar grid
        CalendarGrid(
            currentDate = currentDate,
            onDaySelected = onDaySelected,
            saveDateManager = saveDateManager
        )
    }
}

@Composable
fun CalendarGrid(
    currentDate: Calendar,
    onDaySelected: (Calendar) -> Unit,
    saveDateManager: SaveDateManager
) {
    val context = LocalContext.current

    // Clone the calendar to avoid modifying the original
    val calendar = currentDate.clone() as Calendar

    // Get the year and month
    val year = calendar.get(Calendar.YEAR)
    val month = calendar.get(Calendar.MONTH)

    // Set to the first day of the month
    calendar.set(Calendar.DAY_OF_MONTH, 1)

    // Get the day of week for the first day (0 = Sunday, 1 = Monday, etc.)
    val firstDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1

    // Get the number of days in the month
    val daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)

    // Get today's date for highlighting
    val today = Calendar.getInstance()
    val todayYear = today.get(Calendar.YEAR)
    val todayMonth = today.get(Calendar.MONTH)
    val todayDay = today.get(Calendar.DAY_OF_MONTH)

    // Get all dates with content for this month
    val allDates = saveDateManager.getAllSaveDates()
    Log.d("HistoryScreen", "All dates with content: $allDates")

    // Create a list of day cells (empty for padding + actual days)
    val dayCells = mutableListOf<CalendarDay>()

    // Add empty cells for padding at the start
    for (i in 0 until firstDayOfWeek) {
        dayCells.add(CalendarDay(day = "", isCurrentMonth = false, hasContent = false, isToday = false, date = null, mood = null))
    }

    // Add cells for each day of the month
    for (day in 1..daysInMonth) {
        val date = Calendar.getInstance().apply {
            set(Calendar.YEAR, year)
            set(Calendar.MONTH, month)
            set(Calendar.DAY_OF_MONTH, day)
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }

        // Check if this day has content
        var hasContent = saveDateManager.hasContentOnDate(date)

        // Check if this is today
        val isToday = (year == todayYear && month == todayMonth && day == todayDay)

        // Get mood for this day if available
        var mood: String? = null

        // Format date string to match the format used in storage
        val dateString = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(date.time)

        // Check for mood directly in shared preferences for better performance
        val diaryPrefs = context.getSharedPreferences("diary_prefs", android.content.Context.MODE_PRIVATE)
        val moodContent = diaryPrefs.getString("mood_$dateString", null)

        if (moodContent != null) {
            // Extract the mood value from the content string
            mood = when {
                moodContent.contains("Very Sad") -> "very_sad"
                moodContent.contains("Sad") -> "sad"
                moodContent.contains("Neutral") -> "neutral"
                moodContent.contains("Happy") -> "happy"
                moodContent.contains("Very Happy") -> "very_happy"
                else -> null
            }
            Log.d("HistoryScreen", "Day $day has mood: $mood from shared preferences")

            // If we found a mood, make sure this day is marked as having content
            if (mood != null && !hasContent) {
                hasContent = true
                Log.d("HistoryScreen", "Day $day now marked as having content due to mood")
            }
        } else if (hasContent) {
            // If not found in shared preferences but day has content, try getting from SaveDateManager
            val content = saveDateManager.getContentForDate(date)
            if (content.containsKey(ContentType.MOOD)) {
                val contentMood = content[ContentType.MOOD] ?: ""
                // Extract the mood value from the content string
                mood = when {
                    contentMood.contains("Very Sad") -> "very_sad"
                    contentMood.contains("Sad") -> "sad"
                    contentMood.contains("Neutral") -> "neutral"
                    contentMood.contains("Happy") -> "happy"
                    contentMood.contains("Very Happy") -> "very_happy"
                    else -> null
                }
                Log.d("HistoryScreen", "Day $day has mood: $mood from SaveDateManager")
            }
        }

        // Also check for mood file directly
        if (mood == null) {
            val folderName = "OnLiDi"
            val folder = File(Environment.getExternalStoragePublicDirectory(
                Environment.DIRECTORY_DOCUMENTS), folderName)

            if (folder.exists()) {
                val moodFile = File(folder, "${dateString}_mood.txt")
                if (moodFile.exists()) {
                    try {
                        val moodFileContent = moodFile.readText()
                        mood = when {
                            moodFileContent.contains("Very Sad") -> "very_sad"
                            moodFileContent.contains("Sad") -> "sad"
                            moodFileContent.contains("Neutral") -> "neutral"
                            moodFileContent.contains("Happy") -> "happy"
                            moodFileContent.contains("Very Happy") -> "very_happy"
                            else -> null
                        }
                        hasContent = true
                        Log.d("HistoryScreen", "Day $day has mood: $mood from file")
                    } catch (e: Exception) {
                        Log.e("HistoryScreen", "Error reading mood file", e)
                    }
                }
            }
        }

        Log.d("HistoryScreen", "Day $day final status - hasContent: $hasContent, mood: $mood")

        dayCells.add(CalendarDay(
            day = day.toString(),
            isCurrentMonth = true,
            hasContent = hasContent,
            isToday = isToday,
            date = date,
            mood = mood
        ))
    }

    // Calculate how many cells we need to fill the last row
    val totalCells = dayCells.size
    val remainder = totalCells % 7
    if (remainder > 0) {
        val padding = 7 - remainder
        for (i in 0 until padding) {
            dayCells.add(CalendarDay(day = "", isCurrentMonth = false, hasContent = false, isToday = false, date = null, mood = null))
        }
    }

    // Display the grid
    LazyVerticalGrid(
        columns = GridCells.Fixed(7),
        modifier = Modifier.fillMaxWidth(),
        contentPadding = PaddingValues(4.dp)
    ) {
        items(dayCells) { day ->
            DayCell(
                day = day,
                onDaySelected = onDaySelected
            )
        }
    }
}

@Composable
fun DayCell(
    day: CalendarDay,
    onDaySelected: (Calendar) -> Unit
) {
    // Define mood color first
    val moodColor = when (day.mood) {
        "very_sad" -> Color(0xFFE57373) // Light Red
        "sad" -> Color(0xFFFFB74D)      // Light Orange
        "neutral" -> Color(0xFFFFEE58)   // Light Yellow
        "happy" -> Color(0xFFAED581)     // Light Green
        "very_happy" -> Color(0xFF2E7D32) // Very Dark Green
        else -> null
    }

    // Define colors based on cell state - use mood color as background when available
    val backgroundColor = when {
        !day.isCurrentMonth -> Color.Transparent
        moodColor != null && day.isToday -> moodColor.copy(alpha = 0.9f) // Slightly more opaque for today
        moodColor != null -> moodColor.copy(alpha = 0.7f) // Use mood color as background
        day.hasContent && day.isToday -> MaterialTheme.colorScheme.primaryContainer.copy(alpha = 0.8f)
        day.hasContent -> MaterialTheme.colorScheme.primaryContainer
        day.isToday -> MaterialTheme.colorScheme.primary.copy(alpha = 0.3f)
        else -> MaterialTheme.colorScheme.surface
    }

    val textColor = when {
        !day.isCurrentMonth -> Color.Transparent
        moodColor != null -> Color.Black // Use black text on mood color backgrounds for better readability
        day.hasContent && day.isToday -> MaterialTheme.colorScheme.onPrimaryContainer
        day.hasContent -> MaterialTheme.colorScheme.onPrimaryContainer
        day.isToday -> MaterialTheme.colorScheme.onPrimary
        else -> MaterialTheme.colorScheme.onSurface
    }

    val borderColor = when {
        !day.isCurrentMonth -> Color.Transparent
        day.isToday -> MaterialTheme.colorScheme.primary
        moodColor != null -> moodColor // Use mood color for border when mood is present
        day.hasContent -> MaterialTheme.colorScheme.primary.copy(alpha = 0.7f)
        else -> MaterialTheme.colorScheme.outlineVariant
    }

    val borderWidth = when {
        day.isToday -> 2.dp
        moodColor != null -> 1.5.dp // Use border for mood days
        day.hasContent -> 1.5.dp
        else -> 1.dp
    }

    Box(
        modifier = Modifier
            .aspectRatio(1f)
            .padding(2.dp)
            .border(
                width = borderWidth,
                color = borderColor
            )
            .background(backgroundColor)
            .then(
                if (day.isCurrentMonth && (day.hasContent || day.isToday)) {
                    Modifier.clickable {
                        day.date?.let {
                            Log.d("HistoryScreen", "Day cell clicked: ${SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(it.time)}")
                            Log.d("HistoryScreen", "Has content: ${day.hasContent}, Is today: ${day.isToday}")
                            Log.d("HistoryScreen", "Has mood: ${day.mood ?: "none"}")
                            onDaySelected(it)
                        }
                    }
                } else {
                    Modifier
                }
            ),
        contentAlignment = Alignment.Center
    ) {
        // Stack the content
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // Day number - now the mood color is used as background, so no need for separate indicator
            Text(
                text = day.day,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = if (day.hasContent || day.isToday || day.mood != null) FontWeight.Bold else FontWeight.Normal
                ),
                color = textColor
            )

            // Debug logging for mood
            if (day.mood != null) {
                Log.d("HistoryScreen", "Rendering day ${day.day} with mood background: ${day.mood}")
            }
        }
    }
}



data class CalendarDay(
    val day: String,
    val isCurrentMonth: Boolean,
    val hasContent: Boolean,
    val isToday: Boolean,
    val date: Calendar?,
    val mood: String? = null // Store the mood for this day if available
)

@Composable
fun MoodLegendItem(
    color: Color,
    text: String,
    modifier: Modifier = Modifier
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier.padding(4.dp)
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .background(color = color, shape = CircleShape)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = text,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}
